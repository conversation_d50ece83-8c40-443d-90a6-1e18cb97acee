<template>
  <bargainDialogTop
  :visible="bargainVisible"
  :dialog-width="'585px'"
  class="bargainDialog"
  @dialogClone="dialogClone"
>
  <template slot="right_title">
    <span class="bargain_right_title">发起议价</span>
  </template>
  <template slot="content">
    <div class="spaceBetween bargain_img_box">
      <div class="cutDt_pic">
        <el-image
          :src="shopDetailJson.pic"
          style="width: 100%; height: 100%"
          fit="cover"
        ></el-image>
      </div>
      <div
        :class="
          offerPrice && offerPrice != 0 ? 'cutDt_body1' : 'cutDt_body'
        "
      >
        <div class="text_linTwo cutDt_body_name">
          {{ shopDetailJson.subTitle | tedianFilter }}
        </div>
        <div class="dialog_account_price">
          <span>商品价格：</span><span style="color: #ff720c;">￥{{ shopDetailJson.price }}</span>
        </div>
        <div class="cutDt_price">
          <span
            style="
              font-size: 14px;
              color: #1b1b1b;
              font-weight: 400;
              font-family: PingFang SC;
            "
            >当前最高出价：</span
          >
          <span v-if="maxPrice && maxPrice != 0">¥{{ maxPrice }}</span>
          <span v-else>暂无议价</span>
        </div>
        <div v-if="offerPrice && offerPrice != 0" class="cutDt_price">
          <span
            style="
              font-size: 14px;
              color: #1b1b1b;
              font-weight: 400;
              font-family: PingFang SC;
            "
            >历史最高出价：</span
          >
          <span v-if="offerPrice && offerPrice != 0"
            >¥{{ offerPrice }}</span
          >
        </div>
      </div>
    </div>

    <div class="cut_cnver">注意事项<span style="color:red"></span></div>
    <div
      v-if="productCategoryId == 2 || productCategoryId == 1"
      class="cut_conHtml"
    >
      1、砍价无需支付议价金，砍价金额须高于商品原价格的70%<br />
      2、发起砍价后，若卖家未响应，砍价会在24小时后自动取消 <br />
      3、砍价成功后，买家需在12小时内完成支付，超时议价自动取消 <br />
      4、砍价成功后支付前，其他买家可继续砍价或购买<br />
    </div>
    <div v-else class="cut_conHtml">
      1、砍价无需支付议价金，砍价金额须高于商品原价格的70%<br />
      2、发起砍价后，若卖家未响应，砍价会在24小时后自动取消 <br />
      3、砍价成功后，买家需在12小时内完成支付，超时议价自动取消 <br />
      4、砍价成功后支付前，其他买家可继续砍价或购买<br />
    </div>
    <!-- <div class="spaceStartNotAi cut_checkout_box">
      <IconFont
        v-if="!checked"
        :size="17.14"
        style="margin-right: 6px; cursor: pointer; margin-top: -0px"
        icon="unchecked"
        @click="changChecked(true)"
      />
      <img
        v-if="checked"
        style="width: 17.14px; margin-right: 6px; cursor: pointer"
        src="../../../static/imgs/confirmOrder_order_chebox_icon.svg"
        alt=""
        @click="changChecked(false)"
      />
      <div>
        我已阅读并同意<router-link
          class="cut_checkout_box_text"
          to="/helpCenter?id=99"
          >购买须知</router-link
        >
      </div>
    </div> -->
    <!-- price -->
    <div class="spaceCenter" style="margin-top: 20px;margin-left:30px">
      <div style="color:#ff720c;font-size:22px">¥</div>
      <div class="spaceStart cutDt_pushWrap1">
        <div class="spaceCenter" >
          <input
          v-model="price"
          :style="{ 'font-size':'20.56px' }"
          type="number"
          maxlength="9"
          onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
          class="cutDt_push_ipt1"
          placeholder="请输入"
          @input="priceInput"
        />
        </div>
      </div>
     
    </div>
    
    <div class="spaceCenter">允许出价范围<span style="color: #ff720c;">¥{{ this.maxPrice||Math.ceil(shopDetailJson.price * 0.7) }}～¥{{ shopDetailJson.price }}</span></div>
    <div class="sliderBox">
      <el-slider class="my-slider-box" :show-tooltip="false" :min="this.maxPrice||Math.ceil(shopDetailJson.price * 0.7)" @input="sliderInput" :max="shopDetailJson.price" v-model="sliderValue"></el-slider>
    </div>
    <div class="spaceCenter" style="margin-top:10px">
      <div class="spaceCenter cutDt_submit" @click="addBindPrice">提交</div>
    </div>
  </template>
</bargainDialogTop>
</template>

<script>
import bargainDialogTop from './index2.vue';
import isLogin from '@/utils/isLogin';
import {
getDetail,
topOfferPrice,
freeNegoOffer
} from '@/api/playDetail';
export default{
components:{
    bargainDialogTop
},
props:{
    bargainVisible:{
        type: Boolean,
        default: false,
    },
    productId:{
        type: [String, Number],
        default: '',
    },
},
watch:{
    bargainVisible(newVal, oldVal) {
        if(newVal){
            this.price=''
            this.checked=false
            this.maxPrice=0
            this.offerPrice=0
            topOfferPrice({productId:this.productId}).then(res=>{
            if (res.code == 200) {
                const { hisTopPrice,nowTopPrice, offerPrice } = res.data;
                
                this.offerPrice = (hisTopPrice && hisTopPrice.offerPrice) || '';
                this.maxPrice = nowTopPrice.offerPrice||''
            }
        })
        getDetail(this.productId).then(res=>{
            if (res.code == 200) {
            const { product, productAttributeList, productAttributeValueList } =
            res.data;
            this.shopDetailJson = product;
            // this.price=product.price
            this.sliderValue=product.price
            this.productCategoryId = product.productCategoryId;
            }
        })
        }
        
    },
},
data() {
    return {
        checked:false,
        price:'' ,
        sliderValue:0,
        maxPrice: 0, // 当前最高议价
        offerPrice: 0, //历史最高价
        shopDetailJson: {
            yusuan: '0.00',
        },
        productCategoryId: '',
        isOneFlag:true
    }
},
methods:{
  priceInput(e){
    const val = Number(e.target.value);
    this.price = val;
    const comparePrice = this.maxPrice ? this.maxPrice : Math.ceil(this.shopDetailJson.price * 0.7);
    if (val >= comparePrice && val <= this.shopDetailJson.price) {
      this.sliderValue = val;
    }
  },
  sliderInput(e){
    console.log(e,this.isOneFlag,222222211111)
    this.sliderValue=e
    if(e&&this.isOneFlag){
      this.isOneFlag=false
    }else{
      this.price =e
    }
    
  },
    changChecked(flag) {
        this.checked = flag;
        if (flag) {
            this.checkedFlag = false;
        }
    },
    dialogClone(t){
        this.$emit('bargainClone',t)
    },
     // 确定议价
addBindPrice() {
  if (!isLogin()) {
    this.$router.push({
      path: '/login',
      query: {
        redirect: location.href,
      },
    });
    return;
  }
  if (!this.price || this.price < 0) {
    this.$message.error('请正确输入议价价格!');
    return;
  }
  if (this.price > this.shopDetailJson.price) {
    this.$message.error(`报价须小于商品原价${this.shopDetailJson.price}元`);
    return
  }
  if (this.price.length > 9) {
    this.$message.error('最多输入9位数字!');
    return;
  }
  // if (!this.checked) {
  //   this.$message.error('请阅读并同意议价协议!');
  //   return;
  // }

  this.loading = true;
  let data = {
    // buyType: 3,
    offerPrice: Number(this.price),
    productId: this.productId,
    // #ifdef H5
    // sourceType: 1,
    // #endif
  };
  freeNegoOffer(data)
    .then((res) => {
      if (res.code == 200) {
        this.$message.success(res.data)
        this.dialogClone('submit')
      }
    })
    .finally(() => {
      this.loading = false;
    });
},
}
}
</script>

<style scoped lang="scss">
.bargainDialog /deep/ .dialogBk {
top: 160.259px;
}
.bargain_right_title {
color: #ff720c;
font-family: YouSheBiaoTiHei;
font-size: 32px;
font-style: normal;
font-weight: 400;
line-height: 32px;
}
.bargain_img_box {
margin-top: 29.13px;
border: 1px solid #ffb74a;
background: #fff;
border-radius: 13.712px;
height: 143.119px;
padding: 0px 27.424px 0px 15.426px;
}
.cutDt_body {
height: 111.79px;
font-size: 12px;
color: #909090;
display: flex;
justify-content: space-between;
flex-direction: column;
}
.cutDt_body1 {
height: 111.79px;
font-size: 12px;
color: #909090;
// display: flex;
// justify-content: space-between;
// flex-direction: column;
}
.cutDt_pic {
width: 179.11px;
height: 111.79px;
border-radius: 10.28px;
margin-right: 12px;
overflow: hidden;
flex-shrink: 0;
}
.cutDt_body_name {
font-family: 'PingFang SC';
font-size: 17.14px;
color: #222222;
// margin-bottom: 12.855px;
line-height: normal;
font-weight: 500;
}
.dialog_account_price {
color: #9a9a9a;
font-family: 'PingFang SC';
font-size: 13.712px;
font-style: normal;
font-weight: 400;
line-height: normal;
span {
color: #969696;
}
}
.cut_cnver {
margin: 24px 0px 7.713px 0px;
color: #1b1b1b;
font-family: YouSheBiaoTiHei;
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: normal;
}
.cut_conHtml {
color: #969696;
font-family: 'PingFang SC';
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: 24px; /* 175% */
margin-bottom: 12.855px;
}
.cut_checkout_box {
color: rgba(0, 0, 0, 0.4);
font-family: 'PingFang SC';
font-size: 14px;
font-style: normal;
font-weight: 400;
.cut_checkout_box_text {
color: #ffb74a;
text-decoration-line: underline;
text-decoration-style: solid;
text-decoration-skip-ink: none;
text-decoration-thickness: auto;
text-underline-offset: auto;
text-underline-position: from-font;
}
}
.cutDt_pushWrap1 {
width: 100px;
height: 41px;
// border: 1px solid #ff6716;
// background: var(--btn-background-gradient);
border-radius: 20.56px;
box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
position: relative;
text-decoration: none;
display: flex;
align-items: center;
justify-content:center;
&::before {
content: '' !important;
position: absolute;
top: 0px;
left: 0px;
right: 0px;
bottom: 0px;
border-radius: 20.56px;
background: #fffcf9;
z-index: 0;
margin: 3px;
z-index: 1;
}
}
.cutDt_push_btn {
font-family: YouSheBiaoTiHei;
font-size: 24px;
font-style: normal;
font-weight: 400;
line-height: 24px; /* 85.714% */
letter-spacing: 0.56px;
text-align: center;
color: #ff720c;
cursor: pointer;
margin-left: 20.56px;
position: relative;
z-index: 2;
}
.cutDt_submit {
width: 113.981px;
height: 42.85px;
color: #fff;
text-align: center;
font-family: 'PingFang SC';
font-size: 15.426px;
font-style: normal;
font-weight: 500;
line-height: normal;
letter-spacing: 0.72px;
border: 1px solid #ffddbe;
//   background: rgba(0, 0, 0, 0.4);
//   box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
background: var(--btn-background-gradient);
border: 1px solid #ffddbe;
box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
border-radius: 51.42px;
cursor: pointer;
}
// .cutDt_submit:hover,
// .cutDt_submit:active {
//   background: var(--btn-background-gradient);
//   border: 1px solid #ffddbe;
//   box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
// }
.cutDt_push_ipt1 {
height: 40px;
color: #ff720c;
border: none;
background: transparent;
width: 100%;
outline: none;
font-family: Inter;
font-size: 20.56px;
font-style: normal;
font-weight: 600;
line-height: normal;
letter-spacing: -0.96px;
position: relative;
z-index: 2;
&::placeholder {
font-weight: 400;
font-family: 'PingFang SC';
color: rgba(0, 0, 0, 0.4);
}
&::-webkit-input-placeholder {
font-weight: 400;
font-family: 'PingFang SC';
color: rgba(0, 0, 0, 0.4);
}
&::-moz-input-placeholder {
font-weight: 400;
font-family: 'PingFang SC';
color: rgba(0, 0, 0, 0.4);
}
&::-ms-input-placeholder {
font-weight: 400;
font-family: 'PingFang SC';
color: rgba(0, 0, 0, 0.4);
}
}
.cutDt_push_ipt1:focus {
border: none;
}
.sliderBox{
  background: linear-gradient(to right, rgba(255, 237, 211, .5), rgba(255, 237, 211, .5) 46%, rgba(255, 237, 211, .5));
  height:30px;
  padding:0px 30px;
  /* 你这里没生效啊，主要是因为嵌套写法和 /deep/ 选择器的使用方式不对，建议直接写成扁平结构，且 /deep/ 必须放在最前面 */
  /deep/ .my-slider-box .el-slider__runway {
    height: 14px !important;
    background: linear-gradient(270deg, #ff6a6a, #ffbc6a 49%, #ffe74b) !important;
    margin: 9px 0px;
  }
  /deep/ .my-slider-box .el-slider__bar {
    height: 14px !important;
    background: transparent !important;
  }
  /deep/ .my-slider-box .el-slider__button-wrapper {
    top: -12px !important;
  }
  /deep/ .my-slider-box .el-slider__button {
    width: 26px;
    height: 26px;
    border-width: 2px !important;
    border-style: solid !important;
    border-color: #4fc3f7 !important; /* 默认30%以下的颜色 */
    transform: none !important; /* 去除hover放大效果 */
    transition: none !important; /* 去除hover动画 */
  }

  /* 根据 el-slider__button-wrapper 的 left 百分比动态设置滑块按钮边框颜色，颜色与父级背景渐变一致 */
  /* 0-30% 区间，黄色 */
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 0"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 1"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 2"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 3"] {
    /* 0-30% */
  }
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 0"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 1"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 2"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 3"] .el-slider__button {
    border-color: #ffe74b !important; /* 渐变起始色 */
  }

  /* 30%-60% 区间，橙色 */
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 4"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 5"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 6"] {
    /* 30%-60% */
  }
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 4"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 5"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 6"] .el-slider__button {
    border-color: #ffbc6a !important; /* 渐变中间色 */
  }

  /* 60%-100% 区间，红色 */
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 7"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 8"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 9"],
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 100"] {
    /* 60%-100% */
  }
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 7"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 8"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 9"] .el-slider__button,
  /deep/ .my-slider-box .el-slider__button-wrapper[style*="left: 100"] .el-slider__button {
    border-color: #ff6a6a !important; /* 渐变终止色 */
  }

}

</style>