<template>
  <div>
    <div style="position: relative; top: -10000px">
      <audio ref="audio" src="../../../static/audio/msg.mp3"></audio>
    </div>
    <div class="container">
      <!-- IMUIKIT 相关内容 -->
      <div :class="CanShowRight ? 'hasRight' : ''" class="header">
        <!-- src="https://images2.kkzhw.com/mall/statics/pc/LOGO3.png" -->
        <img
          class="logo"
          src="../../../static/imgs/text_Logo.svg"
          alt="kk_logo"
        />
        <div ref="search" class="search" />
        <div ref="add" class="add" />
        <div class="search-box">
          <el-input
            v-model="keyword"
            :clearable="true"
            style="width: 300px"
            placeholder="搜索聊天记录"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="doSearch"
            ></el-button>
          </el-input>
          <!-- <div v-if="sessionList.length" class="sessionList">
            <div
              v-for="(item, index) in sessionList"
              :key="item.sessionId"
              class="ant-dropdown-trigger conversation-item"
            >
              <session-item
                :item="item"
                :clazz="getClazz(index)"
                @close="closeSearch"
              ></session-item>
            </div>
          </div> -->
        </div>
        <div class="sessionName">
          {{ sessionName
          }}<span v-if="!isTeam" class="serverTime">{{ serverTime }}</span>
          <span
            v-else
            class="iconfontnew icon-kehuqunzu teamMember"
            @click="viewTeamMembers"
          ></span>
        </div>
        <div class="logout-icon" @click="onHide">
          <i class="iconfontnew icon-guanbi"></i>
        </div>
      </div>
      <div class="content">
        <div :class="CanShowRight ? 'right-show' : ''" class="im-box-right">
          <imOrderCard
            :cur-flow-name="curFlowName"
            :order-detail="imOrderDetail"
          ></imOrderCard>
          <imStepCard :flow="imFlow"></imStepCard>
          <div class="im-box-right—bottom-footer"></div>
        </div>
        <div class="left">
          <div ref="avatar" class="avatar-icon" />
          <!-- <div
            :class="{ ['contact-icon']: true, ['active']: model === 'contact' }"
            @click="() => (model = 'contact')"
          >
            <i
              :class="{
                ['iconfont2']: true,
                'iconfont': true,
                'icon-tongxunlu1': true,
              }"
            />
            <div class="icon-label">通讯录</div>
          </div> -->
        </div>
        <!-- 如果hasSelectedSession未打开 则给左右添加圆角，如果hasSelectedSession打开,则CanShowRight是否打开 如果打开不给圆角 否则添加 -->
        <div
          v-show="model === 'chat'"
          :style="{
            borderRadius: hasSelectedSession
              ? CanShowRight
                ? '0px'
                : '0px 0px 30px 30px'
              : '0px 0px 30px 30px',
          }"
          class="right"
        >
          <div
            style="
              box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
              position: relative;
              z-index: 99;
            "
          >
            <div class="im-box-right—bottom-footer"></div>
            <div class="spaceBetween chatgroup">
              <div
                :class="{ ['chat-icon']: true, ['active']: type === 'all' }"
                @click="changeModel('all')"
              >
                <!-- <i class="iconfont icon-message_fill_light" /> -->
                <div class="icon-label">全部</div>
              </div>
              <div
                :class="{ ['chat-icon']: true, ['active']: type === 'p2p' }"
                @click="changeModel('p2p')"
              >
                <!-- <i class="el-icon-user-solid" /> -->
                <div class="icon-label">客服</div>
              </div>
              <div
                :class="{ ['chat-icon']: true, ['active']: type === 'team' }"
                @click="changeModel('team')"
              >
                <!-- <i class="iconfontnew icon-kehuqunzu"></i> -->
                <div class="icon-label">交易群</div>
              </div>
            </div>
            <div
              v-show="type === 'all'"
              ref="conversation"
              class="right-list"
            />
            <div
              v-show="type === 'p2p'"
              ref="conversation2"
              class="right-list"
            />
            <div
              v-show="type === 'team'"
              ref="conversation3"
              class="right-list"
            />
          </div>
          <!-- <el-breadcrumb
            v-if="stepList && stepList.length"
            class="steps"
            separator-class="el-icon-arrow-right"
          >
            <el-breadcrumb-item
              v-for="(item, index) in stepList"
              :key="index"
              :class="stepActive === index ? 'stepActive' : ''"
              >{{ item.name }}</el-breadcrumb-item
            >
          </el-breadcrumb> -->
          <div :class="hasSelectedSession ? 'show' : 'hide'" class="kfbar">
            <div class="bar_list">
              <el-button
                type="primary"
                v-if="!negoId && iMNumber && !iMNumber.startsWith('kkzhwzx')"
                size="small"
                plain
                @click="sendMsgToKf('我有问题急需处理', 0, 'shake')"
                >提醒客服</el-button
              >
              <el-button
                v-if="ishaosan"
                type="primary"
                size="small"
                plain
                @click="startCallKf"
                >开始交易</el-button
              >
            </div>
            <div v-if="showBar" class="bar_list">
              <el-button type="primary" size="small" plain @click="showMyOrder"
                >我要咨询</el-button
              >
            </div>
            <div v-if="showBar" class="bar_list">
              <el-button type="primary" size="small" plain @click="changeKF">{{
                iMNumber && iMNumber.startsWith('kkzhwzx')
                  ? '人工客服'
                  : '换个客服'
              }}</el-button>
            </div>
            <!-- 买家 -->
            <div
              v-if="negoId && identities == 'purchaser'"
              style="display: flex; align-items: center"
              class="bar_list"
            >
              <!-- <el-button
                v-if="canRefoundBack(negoData)"
                type="primary"
                size="small"
                plain
                @click="refoundBack"
                >撤回议价</el-button
              > -->
              <el-button
                v-if="
                  negoData.status == 1 ||
                  (negoData.status == 2 && negoData.sellerOfferPrice)
                "
                type="primary"
                size="small"
                @click="palyPage"
                plain
                >立即购买</el-button
              >
              <el-button
                v-if="negoData.status == 2 && negoData.sellerOfferPrice"
                type="primary"
                size="small"
                @click="huanjiaFun"
                plain
                >还价</el-button
              >
              <div v-if="negoData.status == 0" class="nogoStatusText">
                等待卖家答复
              </div>
              <div v-if="negoData.status == 3" class="nogoStatusText">
                议价已取消
              </div>
            </div>

            <!-- 卖家 -->
            <div
              v-if="negoId && identities == 'seller'"
              style="display: flex; align-items: center"
              class="bar_list"
            >
              <el-button
                v-if="canCancel(negoData)"
                type="primary"
                size="small"
                plain
                @click="doCancel(negoData)"
                >取消议价</el-button
              >
              <!-- v-if="negoData.status == 0" -->
              <el-button
                v-if="negoData.status == 0"
                type="primary"
                size="small"
                @click="agreeAssessFun(negoData)"
                plain
                >同意</el-button
              >
              <el-button
                v-if="negoData.status == 0"
                type="primary"
                size="small"
                @click="huanjiaFun"
                plain
                >还价</el-button
              >
              <el-button
                v-if="negoData.status == 0"
                type="primary"
                size="small"
                @click="disAgreeAssessFun(negoData)"
                plain
                >拒绝</el-button
              >
              <el-button
                v-if="negoData.status != 3"
                type="primary"
                size="small"
                @click="yijiaConfigClick(negoData)"
                plain
                >议价设置</el-button
              >
              <div
                v-if="negoData.status == 2 && negoData.sellerOfferPrice > 0"
                class="nogoStatusText"
              >
                等待买家答复
              </div>
              <div v-if="negoData.status == 3" class="nogoStatusText">
                议价已取消
              </div>
              <div v-if="negoData.status == 1" class="nogoStatusText">
                等待买家购买
              </div>
            </div>
          </div>
          <div
            ref="chat"
            :class="hasSelectedSession ? 'show' : 'hide'"
            class="right-content"
          />
          <div v-if="needFace" class="inputBtn">
            <div class="facebox">
              <div class="faceNote">
                交易前请先完成人脸认证,未认证前无法进行交易
              </div>
              <div>
                <el-button class="btn" @click="goFace">前往人脸认证</el-button>
              </div>
            </div>
          </div>
        </div>
        <div v-show="model === 'contact'" class="right">
          <div ref="contactList" class="right-list" />
          <div ref="contactInfo" class="right-content" />
        </div>
        <div v-show="teamMembersShow" class="team-members-box">
          <div class="spaceEnd">
            <i
              style="margin-right: 20px"
              class="iconfontnew icon-guanbi"
              @click="hideTeamMembers"
            ></i>
          </div>
          <div v-for="item in teamMembers" class="spaceStart member">
            <div class="avatarclick" @click="goChat2(item)">
              <div v-if="item.avatar">
                <img :src="item.avatar" class="custom-avatar" />
              </div>
              <div v-else-if="item.account == bim" class="custom-avatar-buyer">
                {{ item.nickInTeam.slice(-2) }}
              </div>
              <div v-else-if="item.account == sim" class="custom-avatar-seller">
                {{ item.nickInTeam.slice(-2) }}
              </div>
              <div v-else class="custom-avatar-seller">
                {{ item.nickInTeam.slice(-2) }}
              </div>
            </div>
            <div style="margin-left: 10px">
              {{ item.nickInTeam }}
            </div>
            <div
              v-if="item.account.indexOf('kkzhw') == 0"
              style="margin-left: 5px; cursor: pointer"
              @click="goChat2(item)"
            >
              <i class="el-icon-chat-line-square"></i>
            </div>
          </div>
        </div>
      </div>
      <productCard v-if="showProductCardId" @doClose="doClose"></productCard>
      <orderCard v-if="showOrderCardId" @doClose="doOrderClose"></orderCard>
      <myOrder v-if="myorderModal" @hide="myorderModal = false"></myOrder>
    </div>
    <el-dialog
      v-if="showViewer"
      :visible.sync="showViewer"
      :append-to-body="true"
      class="custom-image-viewer"
    >
      <img :src="urlList[0]" width="100%" alt="" />
    </el-dialog>
    <uploadImg
      v-if="showUploadImg"
      :url="uploadImgUrl"
      @hideUploadImg="hideUploadImg"
    ></uploadImg>
    <accountForm
      v-if="showAccountForm"
      :url="accountFormUrl"
      :iswz="iswz"
      @hideAccountForm="hideAccountForm"
    >
    </accountForm>
    <buyerUpPhone
      v-if="showBuyerUpPhone"
      :url="buyerUpPhoneUrl"
      :pre-fill-mobile="preFillMobile"
      @close="hideBuyerUpPhone"
    >
    </buyerUpPhone>
    <spzx
      v-if="showSpzxDialog"
      :dialog-visible="showSpzxDialog"
      :action-obj="actionObj"
      @close="hideSpzx"
    >
    </spzx>
    <sellerDraw
      v-if="showSellerDraw"
      :dialog-visible="showSellerDraw"
      :action-obj="actionObj"
      :action-data="actionData"
      @close="hideSellerDraw"
    >
    </sellerDraw>
    <baopei
      v-if="showBaopeiForm"
      :dialog-visible="showBaopeiForm"
      :action-obj="actionObj"
      :action-data="actionData"
      :process-flow-result="processFlowResult"
      @close="hideBaopei"
    >
    </baopei>

    <score
      v-if="showScoreForm"
      :dialog-visible="showScoreForm"
      :action-obj="actionObj"
      :action-data="actionData"
      @close="hideScore"
    >
    </score>
    <el-dialog
      v-if="baojiaDialogFlag"
      :visible.sync="baojiaDialogFlag"
      :append-to-body="true"
      class="custom-image-viewer"
    >
      <div style="display: flex; align-items: center">
        <div style="width: 100px">议价金额：</div>
        <el-input
          v-model="baojiaNumber"
          type="number"
          @input="baojiaInput"
          placeholder="请输入价格、无需支付议价金"
        ></el-input>
      </div>
      <p style="color: red; text-indent: 86px; font-size: 14px">
        12小时内仅限议价一次，请谨慎填写
      </p>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-button @click="baojiaDialogFlag = false">取消</el-button>
        <el-button @click="baojiaClick" type="primary">确定</el-button>
      </div>
    </el-dialog>
    <kfList v-if="kfListModal" @hide="kfListModal = false"></kfList>
    <bargainDialogBox
      @bargainClone="bargainClone"
      :bargainVisible="bargainVisible"
      :productId="productId"
    />
    <tipDialog
      :visible="refoundBackVisible"
      :right_title="true"
      class="refoundBackDialog"
      @dialogClone="refoundBackDialogClone"
      @dialogSubmit="refoundBackSubmitClone"
    >
      <template slot="content">
        您确定要撤回议价吗？确定后不可撤销。如已支付意向金，将在30分钟内原路退回
      </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
    <myBargainDialog
      :visible="dialogVisibleInden"
      :dialog-width="'523px'"
      :logo="myBargainDialogLogo"
      class="myBargainDialog"
      @dialogClone="dialogClone"
    >
      <template slot="right_title">
        <span class="myBargainDialog_right_title">还价</span>
      </template>
      <template slot="content">
        <div style="width: 322px; margin: 0 auto; text-align: center">
          <el-input
            v-model="counteroffer_price"
            class="myBargainDialog_input"
            style="margin-top: 36px; width: 322px"
            placeholder="请输入还价金额"
          />
          <div class="myBargainDialog_priceSureNote">
            如{{
              identities == 'seller' ? '卖家' : '买家'
            }}同意还价金额，不能无责取消
          </div>
          <div v-if="identities == 'seller'" class="counteroffer_price_box">
            <div>
              <span style="color: red;">*</span> 是否同步修改商品售价为  ¥{{counteroffer_price}}?
            </div>
            <div class="agreeWithOfferRadioPriceBox">
              <el-radio v-model="counterofferRadio" label="1"><span>是的，我要同步</span><el-tag style="margin-left: 5px;" size="mini" type="danger"
              >更快出售</el-tag
            ></el-radio>
              <el-radio style="margin-top: 5px;" v-model="counterofferRadio" label="0"><span>暂不同步</span></el-radio>
            </div>
          </div>
          <!-- <div class="spaceCenter">
            <div
              class="spaceCenter plDt_btn"
              style="margin-bottom: 20px; width: 90%"
              @click="counterPriceSure"
            >
              确认还价
            </div>
          </div> -->
          <div class="myBargainDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="dialogVisibleInden = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="counterPriceSure"
              >确 定</el-button
            >
          </div>
        </div>
      </template>
    </myBargainDialog>
    <myBargainDialog
      :visible="agreeWithVisible"
      :dialog-width="'523px'"
      :logo="myBargainDialogLogo"
      class="myBargainDialog"
      @dialogClone="agreeWithVisible=false"
    >
      <template slot="right_title">
        <span style="position: relative;z-index: 10;" class="myBargainDialog_right_title">同意报价</span>
      </template>
      <template slot="content">
        <div style="padding-top: 20px;">
          <div  style="width: 100%;color: #000;font-size: 16px;" class="orderShop_tit text_linTwo">{{agreeWithVisibleDate && agreeWithVisibleDate.productName}}</div>
          <div style="margin: 10px 0px 10px 0px;color: #9a9a9a;">{{agreeWithVisibleDate && agreeWithVisibleDate.productCategoryName}}</div>
          <div class="agreeWithOfferPriceBox">
            <div class="title">买家当前报价:</div>
            <div class="offerPrice">¥{{agreeWithVisibleDate.offerPrice}}</div>
          </div>
          <div style="margin-top: 20px;font-size: 16px;">
            <span style="color: red;">*</span> 是否同步修改商品售价为  ¥{{agreeWithVisibleDate.offerPrice}}?
            <div class="agreeWithOfferRadioBox">
              <el-radio v-model="radio" label="1">是的，我要同步<el-tag style="margin-left: 5px;" size="mini" type="danger"
              >更快出售</el-tag
            ></el-radio>
              <el-radio style="margin-top: 5px;" v-model="radio" label="0">暂不同步</el-radio>
            </div>
          </div>
          <div class="myBargainDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="agreeWithVisible = false"
              >再考虑一下</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="agreeWithClick"
              >同意报价</el-button
            >
          </div>
        </div>
      </template>
    </myBargainDialog>
    <myBargainDialog
      :visible="yijiaConfigVisible"
      :dialog-width="'520px'"
      :logo="myBargainDialogLogo"
      class="myBargainDialog yijiaConfigDialog"
      @dialogClone="dialogConfigClone"
    >
      <template slot="right_title">
        <span class="myBargainDialog_right_title">议价设置</span>
      </template>
      <template slot="content">
        <el-alert
          title="温馨提示：该设置仅对本商品生效，若此商品价格发生变更，需重新开启次功能"
          type="warning"
          :closable="false"
          style="margin-top: 20px"
        >
        </el-alert>
        <div style="margin-top: 10px" v-if="configLastUpdated">
          上次更新时间：{{ configLastUpdated | formatTimetoSS }}
        </div>
        <div class="yijiaConfigContent">
          <div>
            是否开启自动同意报价功能？<el-tag size="mini" type="danger"
              >便捷出售</el-tag
            >
          </div>
          <el-switch
            @change="agreeConfigClick"
            v-model="yijiaConfigValue.agree"
          >
          </el-switch>
        </div>
        <div style="margin-top: 10px">
          <span style="color: red"> 大于 </span
          >该金额的报价，系统将自动为您同意砍价
        </div>
        <el-input
          v-if="yijiaConfigValue.agree"
          v-model="yijiaConfigValue.agreePrice"
          class="yijiaConfigDialog_input"
          style="margin-top: 10px; width: 322px; height: 40px"
          placeholder="请输入金额"
          type="tel"
          onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
        />
        <div v-if="yijiaConfigValue.agreePrice">预计到手价 <span style="color:red"> ¥{{shouyiPrice}}</span>（代售费¥{{retePriceValue}}）</div>
        <div class="yijiaConfigContent">
          <div>是否开启报价防骚扰功能？</div>
          <el-switch
            @change="preventConfigClick"
            v-model="yijiaConfigValue.prevent"
          >
          </el-switch>
        </div>
        <el-input
          v-if="yijiaConfigValue.prevent"
          v-model="yijiaConfigValue.preventPrice"
          class="yijiaConfigDialog_input"
          style="margin-top: 10px; width: 322px; height: 40px"
          :placeholder="'请输入防骚扰金额0-'+this.negoData.offerPrice"
          type="tel"
          
          onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
          @input="preventPriceChange"
        />
        <div style="margin-top: 10px">
         <span style="color: red"> 小于 </span
          >该金额的报价，系统将自动为您拒绝该议价
        </div>
        <div style="width: 322px; margin: 0 auto; text-align: center">
          <!-- <el-input
            v-model="counteroffer_price"
            class="myBargainDialog_input"
            style="margin-top: 36px; width: 322px"
            placeholder="请输入还价金额"
          /> -->
          <!-- <div class="myBargainDialog_priceSureNote">
          如{{identities=='seller'?'卖家':'买家'}}同意还价金额，不能无责取消
          </div> -->
          <!-- <div class="spaceCenter">
            <div
              class="spaceCenter plDt_btn"
              style="margin-bottom: 20px; width: 90%"
              @click="counterPriceSure"
            >
              确认还价
            </div>
          </div> -->
          <div class="myBargainDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="yijiaConfigVisible = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="yijiaConfigSubmit"
              >保存</el-button
            >
          </div>
        </div>
      </template>
    </myBargainDialog>
  </div>
</template>
<script>
window.doNotConnect = 0;
import kfList from './components/kfList';
import { mapState } from 'vuex';
import _ from 'lodash';
import score from './components/score';
import sellerDraw from './components/sellerDraw';
import spzx from './components/spzx';
import imOrderCard from './components/imOrderCard.vue';
import imStepCard from './components/imStepCard.vue';
import accountForm from './components/accountForm';
import buyerUpPhone from './components/buyerUpPhone';
import uploadImg from './components/uploadImg';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import sessionItem from './sessionItem';
import productCard from './productCard';
import orderCard from './orderCard';
import myOrder from './myOrder';
import baopei from './components/baopei.vue';
import { autorun } from 'mobx';
import bargainDialogBox from '@/components/borderDialog/bargainDialog.vue';
import tipDialog from '@/components/borderDialog/index3.vue';
import myBargainDialog from '@/components/borderDialog/index2.vue';
import {
  ComplexAvatarContainer,
  ConversationContainer, // 会话列表组件
  ChatContainer, // 聊天（会话消息）组件
  AddContainer, // 搜索——添加按钮组件
  SearchContainer, // 搜索——搜索组件
  ContactListContainer, // 通讯录——通讯录导航组件
  ContactInfoContainer, // 通讯录——通讯录详情组件，包含好友列表、群组列表以及黑名单列表
  MyAvatarContainer, // 用户资料组件
} from '@xkit-yx/im-kit-ui';
import '@xkit-yx/im-kit-ui/es/style/css';
import { compile } from 'jsx-web-compiler';
import './iconfont.css';
import '../../assets/newicon/iconfont.css';
import { getImInfo } from '@/api/iminfo.js';
import { getFlowState, detailBySn, getProductCategory ,negotiaSellerNegoSet,getDetail,teamAtKfer} from '@/api/kf.js';
import { getOrderDetail } from '@/api/confirmOrder.js';
import util from '@/utils/index';
import request from '@/utils/request';
import { getCertDetail } from '@/api/safeCenter';
import {
  getBuyerList,
  negotiaCancel,
  offerPrice,
  getNegotiaDetail,
  getSellerList,
  sellerDo,
  sellerOfferPrice,
} from '@/api/myAssess.js';

export default {
  name: 'IMApp',
  components: {
    kfList,
    sellerDraw,
    imOrderCard,
    imStepCard,
    myOrder,
    productCard,
    orderCard,
    sessionItem,
    ElImageViewer,
    uploadImg,
    accountForm,
    buyerUpPhone,
    spzx,
    baopei,
    score,
    bargainDialogBox,
    tipDialog,
    myBargainDialog,
  },
  data: function () {
    return {
      agreeWithVisible:false,
      radio:'0',
      counterofferRadio:'1',
      agreeWithVisibleDate:{},
      yijiaConfigValue: {
        agree: false,
        agreePrice: '',
        prevent: false,
        preventPrice: '',
      },
      shouyiPrice:'',
      configLastUpdated:'',
      identities: '',
      myBargainDialogLogo: '../../../static/imgs/text_Logo.svg',
      counteroffer_price: '',
      dialogVisibleInden: false,
      negoData: {},
      negoId: '',
      refoundBackVisible: false,
      productId: '',
      bargainVisible: false,
      iMNumber: '',
      baojiaNumber: '',
      baojiaObj: {},
      baojiaDialogFlag: false,
      kfListModal: false,
      teamMembers: [],
      showScoreForm: false,
      faceCode: '',
      showBaopeiForm: false,
      preFillMobile: '',
      iswz: false,
      sessionName: '',
      serverTime: '',
      hasSelectedSession: false,
      curFlowName: '',
      imFlow: {},
      CanShowRight: false,
      stepActive: null,
      stepList: [],
      showSellerDraw: false,
      uploadImgUrl: '',
      showUploadImg: false,
      showViewer: false,
      showAccountForm: false,
      accountFormUrl: '',
      showBuyerUpPhone: false,
      buyerUpPhoneUrl: '',
      urlList: [],
      type: 'all',
      model: 'chat',
      showDialog: true,
      myorderModal: false,
      isTeam: null,
      keyword: '',
      sessionList: [],
      showSpzxDialog: false,
      actionObj: {},
      actionData: {},
      processFlowResult: {},
      teamMembersShow: false,
      sim: '',
      bim: '',
      yijiaConfigVisible: false,
      feeRate: {},
      productPrice:null,
    };
  },
  computed: {
    ...mapState({
      flowState: (state) => state.flowState,
      imOrderDetail: (state) => state.imOrderDetail,
      userInfo: (state) => state.userInfo,
    }),
    ishaosan() {
      return this.userInfo.type == 1 && this.flowState.type == 1;
    },
    showProductCardId() {
      return !!this.$store.getters.showProductCardId;
    },
    showOrderCardId() {
      return !!this.$store.getters.showOrderCardId;
    },
    showBar() {
      return this.isTeam === false;
    },
    needFace() {
      return (
        this.flowState.sellerim == this.userInfo.imaccount &&
        this.isTeam &&
        this.userInfo.type == 0 &&
        this.flowState.type == 0 &&
        this.faceCode !== '' &&
        this.faceCode != 2
      );
    },
  },
  watch: {
    flowState(result, from) {
      if (result.processFlow) {
        this.processFlowResult = result;
        this.imFlow = JSON.parse(result.processFlow);
        let findCur;

        if (this.imFlow.mainSteps) {
          Object.keys(this.imFlow.mainSteps).forEach((key) => {
            let ele = this.imFlow.mainSteps[key];
            if (ele.current == 1) {
              findCur = ele;
            }
          });
          if (findCur && findCur.subSteps) {
            Object.keys(findCur.subSteps).forEach((key) => {
              let ele = findCur.subSteps[key];
              if (ele.state == 1) {
                this.curFlowName = ele.name;
              }
            });
          }
        }
      }
    },
    keyword(to, from) {
      if (!to) {
        this.sessionList = [];
      }
    },
    imOrderDetail(to, from) {
      if (to && to.id) {
        this.CanShowRight = true;
      } else {
        this.CanShowRight = false;
      }
    },
    'yijiaConfigValue.agreePrice'(to, from) {
      console.log(to,111111);
      this.ratePrice(this.yijiaConfigValue.agreePrice)
    }
  },
  created() {
    // document.addEventListener('paste', this.onPaste);
  },
  beforeDestroy() {
    this.autorun1 && this.autorun1();
    this.autorun2 && this.autorun2();
  },
  mounted() {
    this.$uikit.render(
      SearchContainer,
      {
        onClickChat: () => {
          this.model = 'chat';
        },
      },
      this.$refs.search
    );
    this.$uikit.render(
      AddContainer,
      {
        onClickChat: () => {
          this.model = 'chat';
        },
      },
      this.$refs.add
    );
    this.$uikit.render(MyAvatarContainer, null, this.$refs.avatar);
    this.$uikit.render(
      ConversationContainer,
      {
        renderSessionName: (options) => {
          const { to, scene } = options.session;
          const { store, nim } = window.__xkit_store__;
          let online = false;
          if (store.eventStore.stateMap.get(to) == 'online') {
            online = true;
          }
          let sessionName = this.getSessionName(scene, to);
          const nickList = this.$store.getters.nickList;
          if (scene == 'p2p' && nickList && nickList.length) {
            let findNick = nickList.find((ele) => ele.account == to);
            if (findNick) {
              sessionName = findNick.nick;
            }
          }
          return compile(
            `<div>{context.online&&<span className="online"></span>}<span className="conversation-item-content-name-forjs">${sessionName}</span></div>`,
            {
              online,
            }
          );
        },
        renderCustomP2pSession: (options) => {
          return null;
        },
        renderCustomTeamSession: (options) => {
          return null;
        },
      },
      this.$refs.conversation
    );

    this.$uikit.render(
      ConversationContainer,
      {
        renderSessionName: (options) => {
          const conversationPartnerId = options.session.to;
          const { store, nim } = window.__xkit_store__;
          let online = false;
          if (
            store.eventStore.stateMap.get(conversationPartnerId) == 'online'
          ) {
            online = true;
          }
          return compile(
            `<div>{context.online&&<span className="online"></span>}${store.uiStore.getAppellation(
              { account: conversationPartnerId }
            )}</div>`,
            {
              online,
            }
          );
        },
        renderCustomP2pSession: (options) => {
          return null;
        },
        renderCustomTeamSession: (options) => {
          return '';
        },
      },
      this.$refs.conversation2
    );
    this.$uikit.render(
      ConversationContainer,
      {
        renderCustomP2pSession: (options) => {
          return '';
        },
        renderCustomTeamSession: (options) => {
          const { scene } = options.session;
          return null;
        },
      },
      this.$refs.conversation3
    );
    this.$uikit.render(
      ChatContainer,
      {
        // 以下是自定义渲染，用 compile 函数包裹 html 就可以了，注意 class 要写成 className
        // 安装并引入： import { compile } from "jsx-web-compiler";
        // renderHeader: () => compile(`<div className="my-header">123</div>`),
        renderHeader: (options) => {
          if (options.id.indexOf('team-') === 0) {
            this.isTeam = true;
          } else {
            this.isTeam = false;
          }
          return null;
        },

        renderEmpty: () =>
          compile(`<div className="empty_box">
            <div className='welcome'></div>
            </div>`),
        renderTeamCustomMessage: (options) => {
          const { msg } = options;
          const { type } = msg;
          if (type === 'notification') {
            return compile(`<div className="hide"></div>`);
          }
          if (type === 'tip') {
            return compile(`<div>
              <div className='common-parse-session-noti'>
                <span className='lingdan'></span>
                ${msg.body}
                </div>
              </div>`);
          }
          if('ps' in msg){
            return compile(`<div>
              <div className='common-parse-session-noti'>
                ${msg.ps}
                </div>
              </div>`);
          }
        },
        renderP2pCustomMessage: (options) => {
          const { msg } = options;
          const { type } = msg;
          if (type === 'tip') {
            return compile(`<div>
              <div className='common-parse-session-noti'>
                <span className='lingdan'></span>
                ${msg.body}
                </div>
              </div>`);
          }
          if('ps' in msg){
            return compile(`<div>
              <div className='common-parse-session-noti'>
                ${msg.ps}
                </div>
              </div>`);
          }
        },
        renderMessageName: (msg) => {
          const { store } = window.__xkit_store__;
          const { from: account, sessionId: teamId, scene, to } = msg;
          const name = store.uiStore.getAppellation({ account, teamId });
          let imnick = '';
          let nickclazz = '';
          if (account.indexOf('kk') === 0 || account.indexOf('kk') === 0) {
            imnick = '官方';
            nickclazz = 'gfmsg';
          }
          if (scene == 'team') {
            try {
              const team = store.teamStore.teams.get(to);
              const { serverExt = {} } = team;
              let obj = JSON.parse(serverExt);
              const { sim, bim } = obj;
              this.sim = sim;
              this.bim = bim;
              if (account !== store.userStore.myUserInfo.account) {
                if (account == sim) {
                  imnick = '卖家';
                  nickclazz = 'smsg';
                } else if (account == bim) {
                  imnick = '买家';
                  nickclazz = 'bmsg';
                }
              }
            } catch (err) {
              console.log(err);
            }
          }
          return compile(
            `<div className="name nickNameBox">
            <span>{context.name}</span>
            {context.imnick?(<span className={context.nickclazz}>{context.imnick}</span>):('')}
            </div>`,
            { name, imnick, nickclazz }
          );
        },
        renderMessageAvatar: (msg) => {
          const { store } = window.__xkit_store__;
          const isSelf = msg.from === store.userStore.myUserInfo.account;
          const goChat = (msg) => {
            const account = msg.from;
            const { scene } = msg;
            if (scene == 'team' && account.indexOf('kkzhw') == 0) {
              const { store } = window.__xkit_store__;
              const sessionId = `p2p-${account}`;
              if (store.sessionStore.sessions.get(sessionId)) {
                store.uiStore.selectSession(sessionId);
              } else {
                store.sessionStore.insertSessionActive('p2p', account);
              }
            }
          };
          if (isSelf) {
            return compile(`<context.MyAvatarContainer canClick={false} />`, {
              MyAvatarContainer,
            });
          } else {
            return compile(
              `<div className="avatatdiv" onDoubleClick={()=>{context.goChat(context.msg)}}><context.ComplexAvatarContainer
                   account={context.msg.from}
                   canClick={false}
                 /></div>`,
              { ComplexAvatarContainer, msg, goChat }
            );
          }
        },
        renderMessageOuterContent: (msg) => {
          const { store } = window.__xkit_store__;

          if (msg.type === 'custom' && msg.attach.type === 'kkmsg_action2') {
            let msgText = msg.attach.body.title;
            const content = msg.attach.body.content;
            const actions = msg.attach.body.action || [];
            const handleActionClick = this.handleActionClick;
            const richClick = this.richClick;
            const canShow = this.canShow;
            const data = msg.attach.data;

            return compile(
              `<div className="kk_custom_msg_wrapper kk_custom_msg_wrapper2">
                  <div className="kk_custom_msg_title" dangerouslySetInnerHTML={{ __html: context.msgText }}></div>
                  <div className="kk_custom_msg_content" onClick={context.richClick} dangerouslySetInnerHTML={{ __html: context.content }}></div>
                  <div className="actions_box">
                  {context.actions.filter(action => context.canShow(action)).map((action, index) => (
                    <div className="kk_custom_msg_action"
                    key={index}
                    onClick={()=>{context.handleActionClick(action,context.data)}}>{action.name}</div>
                  ))}
                  </div>
              </div>`,
              {
                msgText,
                content,
                actions,
                handleActionClick,
                richClick,
                canShow,
                data,
              }
            );
          } else if (
            msg.type === 'custom' &&
            msg.attach.type === 'kkmsg_action'
          ) {
            let msgText = msg.attach.body.title;
            let content = msg.attach.body.content;
            let actions = msg.attach.body.action || [];
            if (msg.attach.body.action2) {
              actions = msg.attach.body.action2;
              actions.forEach((ele) => (ele.actionType = 'action2'));
            }
            const handleActionClick = this.handleActionClick;
            const richClick = this.richClick;
            const canShow = this.canShow;
            const data = msg.attach.data;

            if (msg.scene == 'team') {
              const team = store.teamStore.teams.get(msg.to);
              const { serverCustom = '{}' } = team;
              const serverCustomJson = JSON.parse(serverCustom);
              const { bim, sim } = serverCustomJson;
              const myAccount = store.userStore.myUserInfo.account;
              if (bim == myAccount) {
                let nick = serverCustomJson.bnm || '';
                if (nick) {
                  if (msgText.indexOf('@买家老板') != -1) {
                    msgText = msgText.replace(
                      /@买家老板/g,
                      `<span class="atbuy">@买家${nick}</span>`
                    );
                  }
                  if (content.indexOf('@买家老板') != -1) {
                    content = content.replace(
                      /@买家老板/g,
                      `<span class="atbuy">@买家${nick}</span>`
                    );
                  }
                }
              }
              if (sim == myAccount) {
                let nick = serverCustomJson.snm || '';
                if (nick) {
                  if (msgText.indexOf('@卖家老板') != -1) {
                    msgText = msgText.replace(
                      /@卖家老板/g,
                      `<span class="atsell">@卖家${nick}</span>`
                    );
                  }
                  if (content.indexOf('@卖家老板') != -1) {
                    content = content.replace(
                      /@卖家老板/g,
                      `<span class="atsell">@卖家${nick}</span>`
                    );
                  }
                }
              }
            }
            // console.log('***************');
            // <div className="kk_custom_msg_title_yijia_status"></div>
            return compile(
              `<div className="kk_custom_msg_wrapper">
                 
                  <div className="kk_custom_msg_title" dangerouslySetInnerHTML={{ __html: context.msgText }}></div>
                  <div className="kk_custom_msg_content kk_custom_msg_im_content_style" onClick={context.richClick} dangerouslySetInnerHTML={{ __html: context.content }}></div>
                  <div className="actions_box">
                  {context.actions.filter(action => context.canShow(action)).map((action, index) => (
                    <div className="kk_custom_msg_action"
                    key={index}
                    onClick={()=>{context.handleActionClick(action,context.data)}}>{action.name}</div>
                  ))}
                  </div>
              </div>`,
              {
                msgText,
                content,
                actions,
                handleActionClick,
                richClick,
                canShow,
                data,
              }
            );
          } else if (
            msg.type === 'custom' &&
            msg.attach.type === 'kk_order_msg_fed'
          ) {
            const { title, content } = msg.attach.body;
            const { orderId } = msg.attach.data || {};
            const handleActionClick = this.goOrder;
            return compile(
              `<div>
                  <div className="kk_custom_msg_wrapper curpot" onClick={()=>{context.handleActionClick(context.orderId)}}>
                    <div className="kk_custom_msg_title oneline">
                      { context.title }
                    </div>
                    <div
                      dangerouslySetInnerHTML={{ __html: context.content }}
                      className="kk_custom_msg_content"
                    ></div>
                  </div>
                </div>`,
              { title, content, orderId, handleActionClick }
            );
          } else if (
            msg.type === 'custom' &&
            msg.attach.type === 'kk_product_msg_fed'
          ) {
            const { title, content, type4List = [] } = msg.attach.body;
            const { productId, productSn } = msg.attach.data || {};
            const handleActionClick = this.goProduct;
            const handleQuesClick = (txt) => {
              if (txt == '我要议价') {
                this.productId = JSON.parse(msg.content).data.productId;
                this.bargainVisible = true;

                // let {scene, to, from}=msg
                // let obj={productSn:productSn, msg:{scene, to, from}}
                // this.baojiaObj=obj
                // this.baojiaDialogFlag=true
                return;
              }
              this.handleQuesClick(txt, productSn, msg);
            };
            // <div className="kk_custom_msg_title oneline">
            //           { context.title }
            //         </div>
            return compile(
              `<div>
                  <div className="kk_custom_msg_wrapper curpot">
                    <div
                      onClick={()=>{context.handleActionClick(context.productSn)}}
                      dangerouslySetInnerHTML={{ __html: context.content }}
                      className="kk_custom_msg_content kk_custom_msg_im_content_style kk_custom_msg_im_content_style_border"
                    ></div>
                    {context.type4List.length?<div className="kk_custom_msg_question_tit">常用问题</div>:''}
                    {context.type4List.map((item, index) => (
                    <div className="kk_custom_msg_question"
                    key={index}
                    onClick={()=>{context.handleQuesClick(item)}}>{index+1}、{item}</div>
                  ))}
                    <div className="kk_custom_msg_question_red"

                    onClick={()=>{context.handleQuesClick('我要议价')}}>{context.type4List.length+1}、我要议价</div>
                
                  </div>
                </div>`,
              {
                title,
                content,
                productId,
                productSn,
                handleActionClick,
                type4List,
                handleQuesClick,
              }
            );
          } else {
            return null;
          }
        },
        msgOperMenu: [
          {
            key: 'recall',
            show: 0,
          },
          {
            key: 'reply',
            show: 0,
          },
          {
            key: 'delete',
            show: 0,
          },
          {
            key: 'forward',
            show: 0,
          },
        ],
        // 发送事件
        onSendText: async (data) => {
          const { value = '', scene, to } = data;
          if (!value) {
            return;
          }
          const realText = value.trim();
          if (scene !== 'team' && /^[0-9a-zA-Z]+$/.test(realText)) {
            const params = {
              productSn: realText,
              ignore: 1,
            };
            const res = await detailBySn(params);
            if (res && res.code == 200 && res.data) {
              const data = res.data;
              const attach = util.createProdcutAttach(data);
              const { store } = window.__xkit_store__;
              const myAccount = store.userStore.myUserInfo.account;
              store.msgStore
                .sendCustomMsgActive({
                  scene: scene,
                  from: myAccount,
                  to: to,
                  attach: JSON.stringify(attach),
                })
                .then((res) => {
                  // 让消息滚动到可视区域
                  document.getElementById(`${res.idClient}`).scrollIntoView();
                })
                .catch((err) => {
                  console.log('发送失败', err);
                });
            } else {
              this.sendTxt(data);
            }
          } else {
            this.sendTxt(data);
          }
        },
      },
      this.$refs.chat
    );
    this.$uikit.render(ContactListContainer, null, this.$refs.contactList);
    this.$uikit.render(
      ContactInfoContainer,
      {
        afterSendMsgClick: () => {
          this.model = 'chat';
        },
        onGroupItemClick: () => {
          this.model = 'chat';
        },
      },
      this.$refs.contactInfo
    );
    const { store, nim } = window.__xkit_store__;
    const that = this;
    nim.on('disconnect', (options) => {
      if (window.doNotConnect < 3) {
        // todo 找云信的要 302 才重连
        that.refreshImToken();
      }
    });

    nim.on('logined', () => {
      // 登录或重连成功了
      window.doNotConnect = 0;
    });

    nim.on('willReconnect', function (obj) {
      // 断网恢复会触发disconnect，这里不用刷新 token
      // cmdError, 302, The user name or password is incorrect
      // that.refreshImToken();
    });
    nim.on('msg', function (obj) {
      const { store } = window.__xkit_store__;
      const { body, type, from } = obj;
      that.playSound();
      if (that.negoId) {
        that.getNegotiaDetailData();
      }
      let txt = body;
      if (!txt) {
        txt = '您有一条系统消息,请查收';
      }

      const myAccount = store.userStore.myUserInfo.account;
      if (type == 'notification' || type == 'tip' || myAccount == from) {
        // 系统消息和小铃铛不提示
      } else {
        that.$notify.info({
          customClass: 'imNotify',
          title: '您有新的消息',
          message: txt,
          duration: 5000,
          onClick: () => {
            that.goChat(obj);
          },
        });

        if ('Notification' in window) {
          if (
            Notification.permission === 'granted' &&
            document.visibilityState !== 'visible'
          ) {
            let notification = new Notification('您有新的消息', {
              body: txt,
            });
            notification.onclick = () => {
              window.focus();
              // 可选：关闭通知（有些浏览器在跳转时会自动关闭通知，但这不是标准行为）
              notification.close();
            };
          }
        }
      }
    });
    this.autorun1 = autorun(() => {
      that.$store.dispatch('setImUnreadCount', store.uiStore.sessionUnread);
    });
    this.autorun2 = autorun(() => {
      this.teamMembersShow = false;
      this.teamMembers = [];
      // 切换到某个聊天界面
      this.addPaste();
      const { store } = window.__xkit_store__;

      const selectedSession = store.uiStore.selectedSession;
      if (selectedSession) {
        this.hasSelectedSession = true;
      } else {
        this.hasSelectedSession = false;
      }
      const tempList = selectedSession.split('-');
      const scene = tempList[0];

      tempList.shift();
      const to = tempList.join('-');
      try {
        const team = store.teamStore.teams.get(to);
        if (!team) {
          throw new Error('Team not found');
        }
        const { serverCustom = '{}' } = team;
        const serverCustomJson = JSON.parse(serverCustom);
        const { sim, bim } = serverCustomJson;
        const account = store.userStore.myUserInfo.account;

        if (account == sim) {
          this.identities = 'seller';
        } else if (account == bim) {
          this.identities = 'purchaser';
        }

        if (serverCustomJson.ttp && serverCustomJson.ttp == 7) {
          this.iMNumber = to;
          this.getNegotiaDetailData();
          // getNegotiaDetail( serverCustomJson.negoId).then(res=>{
          //   if(res.code===200){
          //     this.negoData=res.data
          //   }
          // })
        } else {
          this.negoId = '';
        }
      } catch (error) {
        this.negoId = '';
      }

      this.iMNumber = to;
      this.sessionName = this.getSessionName(scene, to);
      if (scene === 'team') {
        this.serverTime = '';
        this.getFlowState(to);
      } else {
        if (to.indexOf('kkzhw') === 0) {
          this.serverTime = '09:30-00:30';
        } else {
          this.serverTime = '';
        }
        this.CanShowRight = false;
        this.stepList = [];
      }
    });
    // 图片查看
    const imbox = document.getElementById('im-box');
    imbox.addEventListener('click', this.previewImg, false);
  },
  methods: {
    ratePrice(price){
      this.retePriceValue = util.times(price, this.feeRate.rate);
      if (this.retePriceValue < this.feeRate.minPrice) {
          this.retePriceValue = this.feeRate.minPrice
      }
      this.shouyiPrice = util.minus(price, this.retePriceValue);
    },
    getNegotiaDetailData() {
      getNegotiaDetail(this.iMNumber).then((res) => {
        if (res.code === 200) {
          this.negoId = res.data.id;
          this.negoData = res.data;
        }
      });
    },
    canCancel(item) {
      return (
        item.status == 1 || (item.status == 2 && item.sellerOfferPrice > 0)
      );
    },
    canRefoundBack(item) {
      return (
        item.status == -1 ||
        item.status == 0 ||
        item.status == 2 ||
        item.status == 5
      );
    },
    doCancel(date) {
      this.$confirm('您确定要取消吗？取消后不可恢复', '二次确认', {
        closeOnClickModal: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.replayFun(date, 2, 2);
      });
    },
    // 确认报价
    agreeAssessFun(date) {
      this.radio='0'
      this.agreeWithVisible=true
      this.agreeWithVisibleDate=date
      // var that = this;
      // this.$confirm('您确定同意当前报价吗？确定后不可撤销', '温馨提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   that.replayFun(date, 1, 1);
      // });
    },
    agreeWithClick(){
      this.replayFun(this.agreeWithVisibleDate, 1, 1,this.radio==1?true:false);
    },
    // 拒绝报价
    disAgreeAssessFun(date) {
      var that = this;
      this.$confirm('您确定拒绝当前报价吗？拒绝后不可撤销', '温馨提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        that.replayFun(date, 2, 0);
      });
    },
    disAgreeAssessFun2(date) {
      var that = this;
      this.$confirm(
        '您确定拒绝并拉黑吗？拒绝拉黑后，系统将帮您自动拒绝该买家对本商品的报价。',
        '温馨提示',
        {
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        that.replayFun(date, 2, 1);
      });
    },
    //议价设置

    async yijiaConfigClick() {
      const [feeRate, productDetail] = await Promise.all([
        getProductCategory(this.negoData.productCategoryId),
        getDetail(this.negoData.productId)
      ]);
      if(feeRate){
        let custom = JSON.parse(feeRate.data.custom);
        this.feeRate = custom.feeRate;
        this.yijiaConfigVisible = true;
      }
      if(productDetail){
        const {product,productAttributeValueList}=productDetail.data
        this.productPrice=product.price
        let config=productAttributeValueList.filter(item=>{
          return item.productAttributeName=='议价黑名单'
        })
        if(config){
          let configValue=JSON.parse(config[0].value)

          const {antiHarassment,autoAccept,lastUpdated}=configValue
          if(lastUpdated){
            this.configLastUpdated=lastUpdated
          }
          this.yijiaConfigValue= {
            agree: autoAccept.enable,
            agreePrice: autoAccept.thresholdAmount,
            prevent: antiHarassment.enable,
            preventPrice:antiHarassment.blockThresholdAmount
          }
        }else{
          this.configLastUpdated = '';
          this.yijiaConfigValue = {
              agree: false,
              agreePrice: '',
              prevent: false,
              preventPrice: ''
          };
        }
       
        
      }
    },
    dialogConfigClone() {
      this.yijiaConfigVisible = false;
    },
    yijiaConfigSubmit(){
     
      const {agree,agreePrice,prevent,preventPrice,}=this.yijiaConfigValue
      if (agreePrice > this.productPrice) {
        this.$message.error(`自动同意金额须低于${this.productPrice}元`);
        return
      }
      if((agree&&!agreePrice)||(prevent&&!preventPrice)){
        this.$message.error(`请完善报价配置`);
        return
      }
      let obj={
          autoAccept:{
            enable:agree,
            thresholdAmount:agreePrice,
          },
          antiHarassment:{
            enable:prevent,
            blockThresholdAmount:preventPrice,
          }
      }

      negotiaSellerNegoSet({productId:this.negoData.productId,quoteSettings:JSON.stringify(obj)}).then(res=>{
        if(res.code=200){
          this.yijiaConfigVisible = false;
          this.$message.success('设置成功')
        }
      })
      
      
      
    },
    agreeConfigClick(e) {
      if (!e) {
        this.yijiaConfigValue.agreePrice = '';
      }
    },
    preventConfigClick(e) {
      if (!e) {
        this.yijiaConfigValue.preventPrice = '';
      }
    },
    preventPriceChange(e){
      if(e>this.negoData.offerPrice){
        this.yijiaConfigValue.preventPrice=this.negoData.offerPrice
        return
      }
    },
    // 回复
    replayFun(date, num, rejectedState,agreeWithFlag=false) {
      this.listLoading = true;
      let data = {
        negoId: date.id,
        status: num,
      };
      if(agreeWithFlag){
        data.changePrice=1
      }
      if (rejectedState || rejectedState == 0) {
        data.rejectedState = rejectedState;
      }
      sellerDo(data)
        .then((res) => {
          if (res.code == 200) {
            this.getNegotiaDetailData();
            this.agreeWithVisible=false
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 确认还价
    counterPriceSure() {
      if (!this.counteroffer_price) {
        this.$message.error('请输入还价金额');
        return;
      }
      if (this.counteroffer_price.length > 9) {
        this.$message.error('最多输入9位数字');
        return;
      }
      if (this.identities == 'purchaser') {
        offerPrice({
          negoId: this.negoData.id,
          price: this.counteroffer_price,
        }).then((res) => {
          this.dialogVisibleInden = false;
          if (res.code == 200) {
            this.$message.success('议价回复成功！');
            this.getNegotiaDetailData();
          }
        });
      }
      if (this.identities == 'seller') {
        sellerOfferPrice({
          negoId: this.negoData.id,
          price: this.counteroffer_price,
          changePrice:this.counterofferRadio=='1'?1:0
        }).then((res) => {
          this.dialogVisibleInden = false;
          if (res.code == 200) {
            this.$message.success('议价回复成功！');
            this.getNegotiaDetailData();
          }
        });
      }
    },
    dialogClone() {
      this.dialogVisibleInden = false;
    },
    // 还价弹窗
    huanjiaFun(date) {
      if(this.identities == 'seller'){
        this.counterofferRadio='1'
      }else{
        this.counterofferRadio='0'
      }
      this.dialogVisibleInden = true;
    },
    // 跳转支付
    palyPage() {
      this.$router.push({
        path: `/confirmOrder?productId=${this.negoData.productId}&from=myAssess&negoId=${this.negoData.id}&productCategoryId=${this.negoData.productCategoryId}`,
      });
      this.$emit('hide');
    },
    // 撤回议价
    refoundBack(date) {
      this.refoundBackVisible = true;
    },
    refoundBackDialogClone() {
      this.refoundBackVisible = false;
    },
    refoundBackSubmitClone() {
      console.log(this.nogoId, 112312331);

      negotiaCancel({
        negoId: this.negoId,
      }).then((res) => {
        // this.listLoading = false;
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.refoundBackVisible = false;
          this.getNegotiaDetailData();
        }
      });
    },
    bargainClone() {
      this.bargainVisible = false;
    },
    baojiaInput(e) {
      // let value=Number(e)

      if (e > 1000000) {
        this.baojiaNumber = 1000000;
      }
    },
    baojiaClick() {
      if (!this.baojiaNumber) {
        return this.$message.error('请填写议价金额');
      }
      if (/^0/.test(this.baojiaNumber)) {
        return this.$message.error('请填写正确的议价金额');
      }
      if (Number(this.baojiaNumber) > 1000000) {
        return this.$message.error('填写金额不能大于1000000');
      }
      this.baojiaDialogFlag = false;
      let text = `${this.baojiaNumber}元卖不卖`;
      let productSn = this.baojiaObj.productSn;
      let msg = this.baojiaObj.msg;
      this.handleQuesClick(text, productSn, msg);
    },
    sendTxt(data) {
      const { store } = window.__xkit_store__;
      const { scene, to, value } = data;
      store.msgStore
        .sendTextMsgActive({
          scene,
          to,
          body: value,
        })
        .then((res) => {
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        });
    },
    addPaste() {
      this.$nextTick(() => {
        let inputBox = document.getElementsByClassName(
          'chat-message-input-textarea'
        )[0];
        if (inputBox) {
          inputBox.removeEventListener('paste', this.onPaste);
          inputBox.addEventListener('paste', this.onPaste);
        }
      });
    },
    onPaste(event) {
      const items = (event.clipboardData || event.originalEvent.clipboardData)
        .items;
      for (let i = 0; i < items.length; i++) {
        if (
          items[i].kind === 'file' &&
          items[i].type.indexOf('image/') !== -1
        ) {
          const file = items[i].getAsFile();
          const { nim, store } = window.__xkit_store__;

          let sessionId = store.uiStore.selectedSession;
          let scene = 'team';
          if (sessionId.indexOf('team-') === 0) {
            sessionId = sessionId.replace('team-', '');
            sessionId = parseInt(sessionId);
          } else {
            scene = 'p2p';
            sessionId = sessionId.replace('p2p-', '');
          }

          store.msgStore
            .sendImageMsgActive({
              scene,
              to: sessionId,
              file,
            })
            .then((res) => {
              document.getElementById(`${res.idClient}`).scrollIntoView();
            });
          event.preventDefault();
        }
      }
    },
    hideTeamMembers() {
      this.teamMembers = [];
      this.teamMembersShow = false;
    },
    viewTeamMembers() {
      const { nim, store } = window.__xkit_store__;
      const selectedSession = store.uiStore.selectedSession;
      const to = selectedSession.replace('team-', '');
      nim.nim.getTeamMembers({
        teamId: to,
        done: (error, obj) => {
          let teamMembers = obj.members || [];
          let accounts = [];
          teamMembers.forEach((ele) => {
            accounts.push(ele.account);
            if (!ele.nickInTeam) {
              ele.nickInTeam = store.uiStore.getAppellation({
                account: ele.account,
                teamId: to,
              });
            }
          });
          nim.nim.getUsers({
            accounts,
            done: (err, users) => {
              users.forEach((item) => {
                const findIt = teamMembers.find(
                  (ele) => ele.account == item.account
                );
                findIt.avatar = item.avatar;
              });
              this.teamMembers = teamMembers;
              this.teamMembersShow = true;
            },
          });
        },
      });
    },
    goFace() {
      this.$router.push({
        path: '/account/identify',
      });
      const { store } = window.__xkit_store__;
      store.uiStore.selectSession('');
    },
    hideBaopei() {
      this.showBaopeiForm = false;
    },
    startCallKf() {
      this.sendMsgToKf('已沟通完成请拉人工客服进群进行交易', 1);
    },
   
// 节流函数
throttle(fn, delay) {
  let lastTime = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastTime >= delay) {
      fn.apply(this, args);
      lastTime = now;
    }
  };
},

sendMsgToKf(txt, fromhansan, shake) {
  // 将节流函数定义为类的属性,避免重复创建
  if (!this._throttledSendMsg) {
    this._throttledSendMsg = this.throttle((txt, fromhansan, shake) => {
      const { nim, store } = window.__xkit_store__;
      const selectedSession = store.uiStore.selectedSession;
      const tempList = selectedSession.split('-');
      const scene = tempList.shift();
      const to = tempList.join('-');
      if (shake == 'shake') {
        teamAtKfer(to).then(res=>{
                console.log(res);
              })
        let myDiv = document.getElementById('im-box');
        myDiv.classList.add('shake-animation');
        setTimeout(function () {
          myDiv.classList.remove('shake-animation');
        }, 3000);
        
        return
      }
      let txtMsg = txt || `我有问题急需处理`;
      let msgBody = {
        scene,
        to,
        body: txtMsg,
      };
      if (scene == 'team') {
        nim
          .getTeamMembers({
            teamId: to,
          })
          .then((res) => {
            let findIt;
            if (fromhansan) {
              findIt = res.find((ele) => ele.type == 'owner');
            } else {
              findIt = res.find((ele) => ele.account.indexOf('kkzhw') === 0);
            }
            
            if(!findIt){
              teamAtKfer(to).then(res=>{
                console.log(res);
              })
              return
            }
            const name = store.uiStore.getAppellation({
              account: findIt.account,
            });
            let newTxt = `@${name} ${txtMsg}`;
            const selectedAtMembers = {
              account: findIt.account,
              appellation: name,
            };
            const ext = util.onAtMembersExtHandler(newTxt, [selectedAtMembers]);
            if (fromhansan) {
              ext.cmdCode = 'recovery_step_1';
              ext.teamId = to;
            }
            msgBody = {
              scene,
              to,
              body: newTxt,
              ext,
            };
            store.msgStore.sendTextMsgActive(msgBody).then((res) => {
              document.getElementById(`${res.idClient}`).scrollIntoView();
            });
          });
      } else {
        store.msgStore.sendTextMsgActive(msgBody).then((res) => {
          document.getElementById(`${res.idClient}`).scrollIntoView();
        });
      }
      if (shake == 'shake') {
        let myDiv = document.getElementById('im-box');
        myDiv.classList.add('shake-animation');
        setTimeout(function () {
          myDiv.classList.remove('shake-animation');
        }, 3000);
      }
    }, 1000);
  }

  this._throttledSendMsg(txt, fromhansan, shake);
},
    handleQuesClick(txt, productSn, msg) {
      const txtMsg = `${txt}`;
      const { store } = window.__xkit_store__;
      const { scene, to, from } = msg;
      let toAccount = to;
      if (to.indexOf('kkzhw') == 0) {
        toAccount = to;
      }
      if (from.indexOf('kkzhw') == 0) {
        toAccount = from;
      }
      store.msgStore
        .sendTextMsgActive({
          scene,
          to: toAccount,
          ext: { 'noyidun': 1, productSn },
          body: txtMsg,
        })
        .then((res) => {
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        });
    },
    getCertDetail() {
      getCertDetail().then((res) => {
        if (res.code == 200) {
          this.faceCode = res.data.faceStatus;
        }
      });
    },
    getFlowState(teamId) {
      getFlowState({ teamId }).then((res) => {
        if (res.code == 200) {
          const result = res.data;
          this.$store.dispatch('setFlowState', result);
          if (result.type == 0) {
            // 交易流程
            this.getCertDetail();
          }
          const orderId = result.orderId;
          if (orderId) {
            this.CanShowRight = true;
            getOrderDetail(orderId).then((res) => {
              if (res.code == 200) {
                this.$store.dispatch('setImOrderDetail', res.data);
              }
            });
          } else {
            this.CanShowRight = false;
          }
        }
      });
    },
    hideUploadImg() {
      this.showUploadImg = false;
    },
    previewImg(event) {
      const { target } = event;
      const { dataset } = target;
      if (dataset && dataset.im) {
        const { store } = window.__xkit_store__;
        const imaccount = dataset.im;
        const sessionId = `p2p-${imaccount}`;
        if (store.sessionStore.sessions.get(sessionId)) {
          store.uiStore.selectSession(sessionId);
        } else {
          store.sessionStore.insertSessionActive('p2p', imaccount);
        }
        this.$store.dispatch('ToggleIM', true);
      } else if (dataset && dataset.open) {
        const { open } = dataset;
        if (open == 'buy') {
          this.$router.push({
            path: '/gameList',
          });
        } else if (open == 'sell') {
          this.$router.push({
            path: '/allSell',
          });
        } else if (open == 'danbao') {
          this.$router.push({
            path: '/assure',
          });
        } else if (open == 'huishou') {
          this.$router.push({
            path: '/recyle',
          });
        }
      } else if (dataset && dataset.imgsrc) {
        this.showViewer = true;
        this.urlList = [dataset.imgsrc];
      } else if (target && target.className) {
        if (target.className.indexOf('kkimimg') != -1) {
          this.showViewer = true;
          this.urlList = [target.src];
        }
      }
    },
    // onCloseView() {
    //   this.showViewer = false;
    // },
    closeSearch() {
      this.sessionList = [];
    },
    getSessionName(scene, to) {
      const { store } = window.__xkit_store__;
      if (scene == 'team') {
        const team = store.teamStore.teams.get(to) || {};
        const subTitle = `(${team.memberNum || 0})`;
        return (team.name || '') + subTitle;
      } else {
        return store.uiStore.getAppellation({
          account: to,
        });
      }
    },
    getClazz(index) {
      index = parseInt(index, 10);
      return `bg-${index % 3}`;
    },
    doSearch() {
      const { nim } = window.__xkit_store__;
      nim.nim.msgFtsInServer({
        keyword: this.keyword,
        msgLimit: 1,
        done: (err, data) => {
          const { msgs = [] } = data;
          this.sessionList = msgs;
        },
      });
    },
    goOrder(orderId) {
      window.open(`/account/orderDetail?orderId=${orderId}`);
    },
    goProduct(productSn) {
      window.open(`/gd/${productSn}`);
    },
    changeModel(type) {
      this.type = type;
    },
    goChat2(item) {
      if (item.account.indexOf('kkzhw') != 0) {
        return;
      }
      const { store } = window.__xkit_store__;
      const { account } = item;
      const sessionId = `p2p-${account}`;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', account);
      }
    },
    goChat(obj) {
      const { sessionId } = obj;
      const { store } = window.__xkit_store__;
      const slist = sessionId.split('-');
      const type = slist.shift();
      const imaccount = slist.join('-');
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive(type, imaccount);
      }
      this.$store.dispatch('ToggleIM', true);
    },
    refreshImToken() {
      const { store, nim } = window.__xkit_store__;
      getImInfo().then(async (res) => {
        // 刷新token
        if (res.code == 200) {
          // token 拿到了就加 1，3 次都连不上说明不是网络问题，可能是限流等其他问题
          window.doNotConnect++;
          const { accid, token } = res.data;
          localStorage.setItem(
            'yximtoken',
            JSON.stringify({ 'account': accid, 'token': token })
          );
          nim.nim.setOptions({
            token,
          });
          nim.connect();
        }
      });
    },
    doOrderClose() {
      this.$store.dispatch('ToggleOrderCardId', '');
    },
    doClose() {
      this.$store.dispatch('ToggleProductCardId', '');
    },
    showMyOrder() {
      this.myorderModal = true;
    },
    changeKF() {
      this.kfListModal = true;
    },
    onHide() {
      this.isTeam = null;
      this.$emit('hide');
    },
    sendMsg(item) {},
    canShow(action) {
      const { accid } = action;
      const { store, nim } = window.__xkit_store__;
      return accid === store.userStore.myUserInfo.account;
    },
    hideAccountForm() {
      this.showAccountForm = false;
    },
    hideBuyerUpPhone() {
      this.showBuyerUpPhone = false;
    },
    hideSellerDraw() {
      this.showSellerDraw = false;
    },
    hideSpzx() {
      this.showSpzxDialog = false;
    },
    hideScore() {
      this.showScoreForm = false;
    },
    richClick(event) {
      // 富文本点击
      const { target } = event;
      if (target.className && target.className.indexOf('copy') != -1) {
        const txt = target.innerText;
        this.copyVal(txt);
      }
    },
    isMute(msg) {
      const { scene, to } = msg;
      let flag = false;
      if (scene == 'team') {
        const { store } = window.__xkit_store__;
        const team = store.teamStore.teams.get(to);
        if (team.serverCustom) {
          const serverCustomJson = JSON.parse(team.serverCustom);
          const { quietIMList = [] } = serverCustomJson;
          const myAccount = store.userStore.myUserInfo.account;
          const findIt = quietIMList.find((ele) => ele == myAccount);
          if (findIt) {
            flag = true;
          }
        }
      }
      return flag;
    },
    playSound() {
      this.$refs.audio.play();
    },
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    handleActionClick(action, data = {}) {
      const { url, fedtemp, msg, pc_url, actionType } = action;

      if (actionType == 'action2') {
        this.$router.push({
          path: pc_url,
          query: data,
        });
        const { store } = window.__xkit_store__;
        store.uiStore.selectSession('');
      } else if (fedtemp == '127_topay_nego') {
        this.$router.push({
          path: `/confirmOrder?from=myAssess&negoId=${data.negoId}&productCategoryId=${data.productCategoryId}`,
        });
      } else if (fedtemp == '127_member_score') {
        this.actionObj = _.cloneDeep(action);
        this.actionData = _.cloneDeep(data);
        this.showScoreForm = true;
      } else if (fedtemp == 'ai_openOrder') {
        this.$router.push({
          path: `/account/orderDetail?orderId=${url}`,
        });
        const { store } = window.__xkit_store__;
        store.uiStore.selectSession('');
      } else if (fedtemp == 'ai_openYijia') {
        // const time = new Date().getTime();
        // this.$router.push({
        //   path: `/playDetail?productId=${url}&autokanjia=1&time=${time}`,
        // });
        // const { store } = window.__xkit_store__;
        // store.uiStore.selectSession('');
        this.productId = url;
        this.bargainVisible = true;
      } else if (fedtemp == '127_baopei_form') {
        this.actionObj = _.cloneDeep(action);
        this.actionData = _.cloneDeep(data);
        this.showBaopeiForm = true;
      } else if (fedtemp === '127_seller_draw_form') {
        this.actionObj = _.cloneDeep(action);
        this.actionData = _.cloneDeep(data);
        this.showSellerDraw = true;
      } else if (fedtemp === '127_openProduct') {
        let purl = `/playDetail?${url}`;
        window.open(purl, '_blank');
      } else if (fedtemp === 'open_baopei') {
        let purl = `/account/payIdentify`;
        window.open(purl, '_blank');
      } else if (fedtemp === '127_scjt') {
        this.uploadImgUrl = url;
        this.showUploadImg = true;
      } else if (fedtemp === '127_img') {
        this.showViewer = true;
        this.urlList = [url];
      } else if (fedtemp === 'wzry_actionSellerCommitAP') {
        this.showAccountForm = true;
        this.accountFormUrl = url;
        this.iswz = true;
      } else if (fedtemp === '127_actionSellerCommitAP') {
        this.showAccountForm = true;
        this.accountFormUrl = url;
        this.iswz = false;
      } else if (fedtemp === '127_actionBuyerUpPhone') {
        const { type, value } = data;
        if (type == 'mobile') {
          this.preFillMobile = value;
        }
        this.showBuyerUpPhone = true;
        this.buyerUpPhoneUrl = url;
      } else if (fedtemp === '127_spzx') {
        this.showSpzxDialog = true;
        this.actionObj = _.cloneDeep(action);
      } else if (url) {
        // 直接发请求，要确认
        if (msg) {
          this.$confirm(msg, '二次确认', {
            closeOnClickModal: false,
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'im_confirm',
          }).then(() => {
            if (url == '#') {
              return;
            }
            request({
              url: url,
              method: 'get',
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
              }
            });
          });
        } else {
          if (url == '#') {
            return;
          }
          request({
            url: url,
            method: 'get',
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message);
            }
          });
        }
      }
    },
  },
};
</script>
<style lang="scss">
.kk_custom_msg_im_content_style {
  .kk_custom_msg_title {
    display: none;
  }
  .twoLine {
    color: var(--Black, #1b1b1b);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    // line-height: 18px;
    letter-spacing: 0.56px;
  }
  .msg-red {
    color: var(--Dark-Orange, #ff720c);

    /* 新/价标/小号 */
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 103%; /* 16.48px */
    letter-spacing: -0.4px;
    margin-top: 12px;
  }
  .kk_custom_msg_content {
    border-bottom: 0.5px solid rgba(150, 150, 150, 0.4);
    margin-bottom: 10.283px;
    padding-bottom: 10.283px;
  }
}
.kk_custom_msg_im_content_style_border {
  border-bottom: 0.5px solid rgba(150, 150, 150, 0.4);
  margin-bottom: 10.283px;
  padding-bottom: 10.283px;
}
.custom-image-viewer {
  .el-dialog {
    margin-top: 1vh !important;
  }
}
.lingdan {
  width: 20px;
  height: 14px;
  background: url('../../../static/lingdan.jpg') no-repeat;
  background-size: contain;
  position: relative;
  display: inline-block;
  top: 2px;
}
.kkimaimg {
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.24px;
  color: #4285f4;
  // background: linear-gradient(
  //   90deg,
  //   #4285f4 11.95%,
  //   #5c99ff 31.36%,
  //   #bbd5ff 91.25%
  // );
  // background-clip: text;
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;
}
.kkimaimg:hover {
  color: #4285f4;
  // background: linear-gradient(
  //   90deg,
  //   #4285f4 11.95%,
  //   #5c99ff 31.36%,
  //   #bbd5ff 91.25%
  // );
  // background-clip: text;
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;
}
.im_confirm {
  position: absolute;
  top: 50%;
  right: 200px;
  margin-top: -100px;
  .el-button--default:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
  .el-button--primary {
    color: #409eff;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
  }
  .el-button--primary:focus {
    color: #409eff;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
  }
  .el-message-box__headerbtn:hover .el-message-box__close {
    color: #409eff;
  }

  .el-button--primary:hover {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}
.chat-message-input-textarea {
  padding: 15px 0px 0px 21px;
  min-height: 90px !important;
}
.chat-message-input {
  padding: 50px 32px 28px 32px;
}
</style>
<style lang="scss" scoped>
.refoundBackDialog /deep/ .dialog3_content {
  text-align: left;
}
.refoundBackDialog /deep/ .el-dialog__body {
  min-height: 279px !important;
  height: auto;
}
.inputBtn {
  .facebox {
    text-align: center;
    background: #fff;
    padding: 50px;
  }
  display: flex;
  position: absolute;
  z-index: 90;
  left: 237px;
  bottom: 17px;
  background-color: rgba(2, 2, 2, 0.2);
  height: 568px;
  width: 535px;
  z-index: 190;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .faceNote {
    margin: 10px;
    font-size: 16px;
    color: #666;
  }
  /deep/ .el-button--default {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
  /deep/ .el-button--default:focus,
  /deep/ .el-button--default:hover {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}
/deep/ .chat-message-list-tobottom {
  z-index: 99;
}
/deep/ .icon-zhanneixiaoxi {
  font-size: 16px;
  margin-right: 5px;
}
/deep/.chat-message-list-item-wrap {
  padding: 6px 0px;
}
/deep/.chat-message-list-item-wrap .ant-avatar {
  margin-right: 10px;
}
.sessionName {
  position: fixed;
  // left: 300px;
  left: 276px;
  font-family: 'PingFang SC';
  font-size: 16px;
  color: #1b1b1b;
  font-weight: 500;
  .serverTime {
    // color: #ff9a3d;
    background: var(--btn-background-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 8px;
  }
}
.hasRight {
  .sessionName {
    // left: 522px;
    left: 576px;
  }
}
.show {
  // width: 570px !important;
  width: 578px !important;
}
.hide {
  width: 0px !important;
}
.im-box-right {
  width: 0;
  border-left: 0;
  overflow: auto;
  margin-left: 1px;
  height: 578px;
  border-top: 0;
  transition: all 0.3s;
  border-radius: 0px 0px 0px 25.71px;
  position: relative;
  z-index: 102;
}
.im-box-right.right-show {
  // border: 1px solid #e8e8e8;
  width: 300px;
  padding: 17.14px 0px 0px 22.282px;
  background: #fcfcfc;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.im-box-right—bottom-footer {
  width: 100%;
  height: 48px;
  // background: red;
  // border-radius: 0px 0px 0px 30px;
  position: absolute;
  bottom: 0px;
  left: 0px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 45%,
    #fff 100%
  );
  z-index: 2;
}
/deep/ .actions_box {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  .kk_custom_msg_action {
    margin-left: 10px;
    padding: 0 10px;
  }
}
/deep/ .kkimimg {
  height: 200px;
  object-fit: contain;
  cursor: pointer;
}
.search {
  display: none;
}

.el-reset-clazz {
  /deep/ .el-input.is-active .el-input__inner,
  /deep/ .el-input__inner:focus,
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: #409eff;
    outline: 0;
  }
  /deep/ .el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #409eff;
  }
  /deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #409eff;
    color: #fff;
  }
  /deep/ .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #409eff;
  }
  /deep/ .el-checkbox__input.is-checked .el-checkbox__inner,
  /deep/ .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  /deep/ .el-dialog__headerbtn:focus .el-dialog__close,
  /deep/ .el-dialog__headerbtn:hover .el-dialog__close {
    color: #409eff;
  }
  /deep/ .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    color: #409eff;
    background-color: #fff;
  }
  /deep/ .el-form-item.is-error .el-input__inner,
  /deep/ .el-form-item.is-error .el-input__inner:focus,
  /deep/ .el-form-item.is-error .el-textarea__inner,
  /deep/ .el-form-item.is-error .el-textarea__inner:focus,
  /deep/ .el-message-box__input input.invalid,
  /deep/ .el-message-box__input input.invalid:focus {
    border-color: #409eff;
  }
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: #409eff;
  }
  /deep/ .el-select .el-input__inner:focus {
    border-color: #409eff;
  }
  /deep/ .el-button--primary.is-plain:focus {
    color: #409eff;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
}

.search-box {
  display: none;
  /deep/ .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  /deep/ .el-input__suffix {
    margin-top: -4px;
  }
}
.sessionList {
  position: absolute;
  top: 49px;
  z-index: 99;
  width: 300px;
  border: 1px solid #ccc;
  .conversation-item {
    cursor: pointer;
    border-bottom: 1px solid #ccc;
    background: #f2f2f1;
  }
  .conversation-item:hover {
    background: #e2e2e2;
  }
  .conversation-item:last-child {
    border-bottom: 0;
  }
}
.kfbar {
  display: flex;
  position: absolute;
  z-index: 90;
  width: calc(100% - 345px);
  // left: 292px;
  right: -33px;
  bottom: 125px;
  background: none;
  height: 40px;
  line-height: 40px;
  .bar_list {
    height: 40px;
    font-family: 'PingFang SC';
    margin-right: 10px;
    .el-button--primary {
      width: 88px;
      border-radius: 30px;
      color: #ff7a00;
      background: linear-gradient(86.87deg, #ff7a00 3.31%, #ffe3c0 142.11%);
      position: relative;
      border: none;
    }
    .el-button--primary /deep/ span {
      position: relative;
      z-index: 20;
    }
    .el-button--primary::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 50px; /* 圆角需与主按钮一致 */
      background: #fff6eb; /* 内部背景色 */
      z-index: 1;
      margin: 2px; /* 边框宽度 */
    }
    .el-button--primary:focus {
      color: #ff7a00;
      // background: #fff6eb;
      // border: 2px solid #ff7a00;
      border: none;
    }
    .el-message-box__headerbtn:hover .el-message-box__close {
      color: #409eff;
    }

    .el-button--primary:hover {
      color: #ff7a00;
      // background: #fff6eb;
      // border: 2px solid #ff7a00;
      border: none;
    }
  }
  .bar_item {
    border: 1px solid #fe5a1e;
    color: #fe5a1e;
    padding: 0 5px;
    border-radius: 5px;
    margin-left: 10px;
    height: 28px;
    line-height: 28px;
    cursor: pointer;
  }
  .bar_item:hover {
    background-color: #efefef;
  }
}
/deep/ .common-parse-session-mention {
  color: #4285f4;
  font-family: 'PingFang SC';
  letter-spacing: 0.56px;
  font-weight: 400;
}
/deep/ .chat-message-list {
  padding: 20px 32px 68px 32px;
  &::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道的背景色 */
    width: 0; /* 隐藏滚动条轨道 */
    height: 0; /* 隐藏滚动条轨道 */
  }
  &::-webkit-scrollbar {
    width: 5px; /* 设置滚动条的宽度 */
    height: 5px; /* 设置滚动条的高度 */
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
  }
  .chat-message-list-item-body {
    background: #f9f6f3;
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
  }
  .gfmsg {
    display: inline-block;
    width: 37px;
    height: 18px;
    font-family: 'PingFang SC';
    border-radius: 12px;
    border: 1px solid #ff9a3d;
    // padding: 2px 8px;
    font-size: 9px;
    background: var(
      --Tag-Orange-Stroke,
      linear-gradient(87deg, #ff7a00 3.31%, #ffe3c0 142.11%)
    );
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
/deep/ .nickNameBox {
  display: flex;
  align-items: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.56px;
}
/deep/ .curpot {
  cursor: pointer;
}

/deep/ .kk_custom_msg_wrapper {
  border-radius: 20px;
  box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
  background-color: #fff;
  font-size: 14px;
  overflow: hidden;
  padding: 16px;
  width: 350px;
  position: relative;
  .at {
    color: #409eff;
  }
  .atbuy {
    color: #233cf8;
  }
  .atsell {
    color: #ea4e4e;
  }
  .kk_custom_msg_title_yijia_status {
    width: 100px;
    height: 100px;
    background: red;
    position: absolute;
  }
  .kk_custom_msg_question {
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 183.333% */
    letter-spacing: 0.24px;
    background: linear-gradient(
      90deg,
      #4285f4 11.95%,
      #5c99ff 31.36%,
      #bbd5ff 91.25%
    );

    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .kk_custom_msg_question_red {
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 183.333% */
    letter-spacing: 0.24px;
    color: #ff720c;
  }
  .kk_custom_msg_question_tit {
    color: var(--Black, #1b1b1b);

    /* 新/文本/小号加强 */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 128.571% */
    letter-spacing: 0.56px;
    margin-bottom: 6.856px;
  }
  // .kk_custom_msg_question:hover {
  //   color: #b50000;
  // }
  .el-tag {
    white-space: normal;
    line-height: 24px;
    height: auto;
  }
  .copy.iconfontnew {
    font-size: 12px;
    font-weight: 400;
  }
  .copy::after {
    content: '\e60d';
    color: #5cb6ff;
    margin-left: 5px;
    cursor: pointer;
    font-size: 14px;
  }
  .oneline {
    max-width: 260px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .twoLine {
    max-width: 260px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .kk_custom_msg_title {
    line-height: 24px;
    max-width: none;
    word-wrap: break-word;
  }
  .kk_custom_msg_content {
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.24px;
    color: #969696;
    word-wrap: break-word;
  }
  .kk_custom_msg_action {
    margin-top: 10px;
    text-align: center;
    height: 27.424px;
    line-height: 27.424px;
    padding: 0px 20px;
    border-radius: 20px;
    cursor: pointer;
    color: #409eff;

    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;
    overflow: hidden;
    background: linear-gradient(
      89.5deg,
      rgba(66, 133, 244, 0.1) 11.95%,
      rgba(92, 153, 255, 0.1) 31.36%,
      rgba(187, 213, 255, 0.1) 91.25%
    );

    color: #4285f4;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.24px;
    &::before {
      content: '';
      position: absolute;
      top: 0px;
      left: 0px;
      right: 0;
      bottom: 0;
      border-radius: inherit;
      padding: 2px; /* 边框宽度 */
      box-sizing: border-box;
      background: linear-gradient(
        89.5deg,
        #4285f4 11.95%,
        #5c99ff 31.36%,
        #bbd5ff 91.25%
      );
      -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: destination-out;
      mask-composite: exclude;
    }
  }

  .kk_custom_msg_action:hover {
    // background: #409eff;
    // border-color: #409eff;
    // color: #fff;
  }
}
/deep/ .kk_custom_msg_wrapper2 {
  width: 200px;
}
.custom-avatar {
  width: 36px;
  height: 36px;
  overflow: hidden;
  border-radius: 30px;
  object-fit: contain;
}

.custom-avatar-buyer {
  width: 36px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 30px;
  color: #fff;
  background: #233cf8;
}
.custom-avatar-seller {
  width: 36px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 30px;
  color: #fff;
  background: #ea4e4e;
}
.team-members-box {
  .avatarclick {
    cursor: pointer;
  }
  .member {
    padding: 5px 10px;
  }
  width: 200px;
}
.teamMember {
  cursor: pointer;
  margin: 0 0 0 10px;
  top: 3px;
  position: relative;
}
.container {
  height: 628px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  border-radius: 30px;
  background: #fcfcfc;
  /deep/ .conversation-item {
    cursor: pointer;
    background: #fcfcfc;
  }
  /deep/ .conversation-item:hover {
    background: #feefe3;
  }
  /deep/ .conversation-item:last-child {
    border-bottom: 0;
  }
  // /deep/ .conversation-list-wrapper {
  //   background: #f6f8fa;
  //   .conversation-item:hover {
  //     background: #dedede;
  //   }
  /deep/ .conversation-item.conversation-item-select {
    background: #feefe3;
  }
  // }
}

.header {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // border-bottom: 1px solid #f8f8f8;
  padding: 9.47px 14.568px 8.57px 21.425px;
  border-radius: 25.71px 25.71px 0px 0px;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
  background: #f8f8f8;
  position: relative;
  z-index: 130;
  .search {
    // display: none;
    width: 200px;
    /deep/ .search-search-wrapper-button {
      background: #fff;
    }
  }
  .add {
    display: none;
  }
  .logo {
    width: 96px;
    object-fit: contain;
    display: block;
    margin: 20px 0;
  }
}
.steps {
  position: absolute;
  z-index: 299;
  width: 538px;
  background: #f6f8fa;
  left: 310px;
  padding: 10px 40px;
  display: flex;
  justify-content: space-around;
  background: #e8effa;
  border-radius: 0;
  margin: 0px 0;
  /deep/ .el-breadcrumb__inner {
    color: #000;
  }
  /deep/ .el-breadcrumb__inner:hover {
    color: #000;
  }
  /deep/ .el-breadcrumb__separator {
    color: #595b5f;
  }
  .stepActive {
    font-weight: 700;
  }
}
/deep/ .conversation-list-wrapper {
  &::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道的背景色 */
    width: 0; /* 隐藏滚动条轨道 */
    height: 0; /* 隐藏滚动条轨道 */
  }
  &::-webkit-scrollbar {
    width: 5px; /* 设置滚动条的宽度 */
    height: 5px; /* 设置滚动条的高度 */
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
  }
  background-color: #fcfcfc;
  border-radius: 0px 0px 0px 25.71px;
  .ant-empty-description {
    display: none;
  }
  .online {
    background: #57e600;
    border: 1px solid #fff;
    border-radius: 50%;
    height: 10px;
    left: 13px;
    position: absolute;
    top: 13px;
    width: 10px;
  }
}
/deep/ .conversation-item {
  width: calc(100% - 8px);
  transition: none !important;
  background-color: #f5f5f5;
  border-top: 1px solid #ececec;
}
/deep/ .conversation-item-content-name {
  display: block;
  font-size: 12px;
  width: 84px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  letter-spacing: 0.24px;
}
/deep/ .conversation-item-content-name-forjs {
  display: block;
  width: 84px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/ .conversation-item-content-msg-body {
  font-family: 'PingFang SC';
  font-size: 10.283px;
  font-weight: 500;
}
.search {
  width: 50%;
}

.add {
  margin-left: 20px;
}

.content {
  width: 100%;
  height: 578px;
  display: flex;
}

.left {
  display: none;
  width: 60px;
  border-right: 1px solid #e8e8e8;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-icon {
  margin: 20px 0 25px 0;
  border-radius: 50%;
  border: 1px solid #e8e8e8;
  display: none;
}

.iconfont {
  font-size: 24px;
}

.chat-icon,
.contact-icon {
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.active {
  color: #2a6bf2;
}

.logout-icon {
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  margin-left: 11px;
  // margin-right: 15px;
}

.icon-label {
  font-size: 12px;
  text-align: center;
}

.right {
  position: relative;
  flex: 1;
  display: flex;
  overflow: hidden;
  // border-radius: 0px 0px 30px 30px;
  .chatgroup {
    width: 243.388px;
    padding: 17.14px 19.711px;
    // border-right: 1px solid #e8e8e8;
    font-family: 'PingFang SC';
    .active {
      .icon-label {
        color: #fff;
        border: 0;

        // width: 65.132px;
        // height: 25.71px;

        // padding: 7px 11px;
        border-radius: 60px;
        background: var(--btn-background-gradient);
        box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
      }
    }
    .icon-label {
      // padding: 7px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 65.132px;
      height: 25.71px;
      font-size: 10.283px;
      font-weight: 500;
      font-family: 'PingFang SC';
      letter-spacing: 0.24px;
      // flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 60px;
      border: 1px solid rgba(255, 255, 255, 0.4);
      background: rgba(210, 210, 210, 0.4);
      box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    }
  }
}

.right-list {
  width: 243.388px;
  height: 514px;
  // border-right: 1px solid #e8e8e8;
}
/deep/ .gficon {
  background: #70dccf;
  color: #fff;
  border-radius: 2px;
  padding: 2px 4px;
  display: inline-block;
  margin-left: 10px;
  font-size: 11px;
  margin-top: 0;
  position: absolute;
  top: 12px;
}
/deep/ .nickname {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/ .gfmsg {
  // width: 34px;
  background: #70dccf;
  border-radius: 5px;
  // padding: 2px 3px;
  color: #fff;
  text-align: center;
  font-size: 12px;
  margin-left: 6px;
}

/deep/ .bmsg {
  width: 34px;
  height: 18px;
  // background: #233cf8;
  border-radius: 12px;
  // border: 1px solid #bbd5ff;
  // background: linear-gradient(
  //   90deg,
  //   #4285f4 11.95%,
  //   #5c99ff 31.36%,
  //   #bbd5ff 91.25%
  // );

  background: linear-gradient(
    89.5deg,
    #bbd5ff 11.95%,
    #5c99ff 71.84%,
    #4285f4 91.25%
  );

  border-radius: 5px;
  padding: 2px 3px;
  color: #fff;
  text-align: center;
  font-size: 9px;
  font-family: Inter;
  margin-left: 10px;
  border-radius: 12px;
  position: relative;
  z-index: 2;
  &::before {
    content: '买家';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    margin: 1px;
    border-radius: 12px;
    background: linear-gradient(
      89.5deg,
      #4285f4 11.95%,
      #5c99ff 31.36%,
      #bbd5ff 91.25%
    );
    font-size: 9px;
    font-family: Inter;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  #text {
    position: relative;
    z-index: 200;
  }
}
/deep/ .smsg {
  // width: 34px;

  // background: #ea4e4e;
  border: 1px solid #ff002e;
  background: var(
    --Tag-Red-BG,
    linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%)
  );
  border-radius: 12px;
  padding: 2px 8px;
  font-family: 'PingFang SC';
  color: #fff;
  text-align: center;
  letter-spacing: 0.2px;
  font-size: 9px;
  margin-left: 6px;
  // margin-top: -2px;
}

/deep/ .chat-message-list-item-content {
  margin-top: 3px;
}

.right-content {
  // width: 675px !important;
  width: 570px;
  position: relative;
  /deep/ .chat-wrap {
    background: #fcfcfc;
    border-radius: 0px 0px 30px 30px;
  }
  /deep/ .chat-action-wrap {
    display: none;
  }
  /deep/ .chat-message-input-wrap {
    border: none !important;
    border-radius: 24px;
    background: #f2f2f2;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  }
}
/deep/ .chat-message-list .chat-message-list-stranger-noti {
  display: none;
}
.icon-guanbi:hover {
  background: #e3e4e6;
  cursor: pointer;
}
.icon-guanbi {
  font-size: 30px;
}
/deep/ .chat-message-input-icon-upload {
  display: none;
}
/deep/ .imcard {
  .imline {
    border-bottom: 1px dashed #ccc;
    margin: 10px 0;
  }
  .avatarImg {
    width: 30px;
    height: 30px;
    border-radius: 30px;
  }
  .name {
    margin-left: 10px;
  }
  .imtxt {
    margin-right: 43px;
  }
  .ima {
    cursor: pointer;
    color: #4285f4;
  }
}
/deep/ .avatatdiv {
  cursor: pointer;
  height: 35px;
  .common-complex-avatar-wrapper {
    cursor: pointer !important;
  }
}
.myBargainDialog_priceSureNote {
  margin-top: 12px;
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.24px;
}
.myBargainDialog_input {
  background: var(--btn-background-gradient);
  width: 322px;
  height: 52px;
  border-radius: 50px;
  &::before {
    content: '' !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    z-index: 0;
    margin: 3px;
    position: absolute;
    z-index: 1;
  }
  /deep/ .el-input__inner {
    border: none;
    width: 318px;
    height: 48px;
    margin-top: 2px;
    border-radius: 50px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    position: relative;
    z-index: 3;
    color: #1b1b1b;
    &::placeholder {
      color: #969696;
    }
  }
}
.yijiaConfigDialog_input {
  background: var(--btn-background-gradient);
  width: 322px;
  height: 36px;
  border-radius: 50px;
  &::before {
    content: '' !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    z-index: 0;
    margin: 2px;
    position: absolute;
    z-index: 1;
  }
  /deep/ .el-input__inner {
    margin-left: 2px;
    border: none;
    width: 318px;
    height: 36px;
    margin-top: 2px;
    border-radius: 50px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    position: relative;
    z-index: 3;
    color: #1b1b1b;
    &::placeholder {
      color: #969696;
    }
  }
}
.myBargainDialog_right_title {
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.myBargainDialog {
  /deep/ .el-dialog__body {
    min-height: 300px;
  }
  /deep/ .doalog2_left_logo {
    width: 135px;
    height: 40px;
  }
  /deep/ .el-dialog__body {
    padding: 38px 44px 38px 44px;
  }
  /deep/ .dialogBk {
    width: 237.173px;
    height: 249.494px;
    top: 25px;
    right: 142.83px;
  }
}
.yijiaConfigDialog {
  /deep/ .dialogBk {
    width: 237.173px;
    height: 249.494px;
    top: 70px;
    right: 142.83px;
  }
}
.myBargainDialogContent_dialog-footer {
  margin-top: 22px;
  display: flex;
  justify-content: center;
  .btn {
    width: 129px;
    height: 46px;
    border-radius: 60px;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.64px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.btnCancel {
  position: relative;
  z-index: 1;
  background: var(--btn-background-gradient);

  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background: #fff;
    margin: 3px;
    border-radius: 60px;
  }
  /deep/ span {
    position: relative;
    z-index: 3;
    background: var(--btn-background-gradient);
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
    -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
  }
}
.btnSubmit {
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  margin-left: 16px;
}
/deep/ .el-button {
  border: none;
}
.nogoStatusText {
  background: #ecf5ff;
  height: 26px;
  display: flex;
  align-items: center;
  padding: 0px 10px;
  border-radius: 6px;
  margin-left: 10px;
  color: #409eff;
  font-size: 14px;
}
.yijiaConfigContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}
.agreeWithOfferPriceBox{
  width: 100%;
  height: 40px;
  border-radius: 10px;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  font-family: PingFang Sc;
  font-size: 16px;
  padding-left: 10px;
  .title{
  }
  .offerPrice{
    color: red;
  }
}
.counteroffer_price_box{
  margin-top: 20px;font-size: 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .agreeWithOfferRadioPriceBox{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 10px;
  }
}

.agreeWithOfferRadioBox{
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}
</style>
